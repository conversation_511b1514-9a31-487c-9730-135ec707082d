cmake_minimum_required(VERSION 3.10)

project(map_engine VERSION 1.0)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)

cmake_policy(SET CMP0135 NEW)

option(ENABLE_GTEST "Enable Gtest" OFF)
option(ENABLE_GMOCK "Enable Google Mock" ON)
option(BUILD_DEBUG "build type debug " ON)
if (BUILD_DEBUG)
    add_definitions(-DAURORA_DEBUG)
    set(CMAKE_BUILD_TYPE "Debug")
else()
    set(CMAKE_BUILD_TYPE "Release")
endif()

set(PROJECT_BINARY_DIR ${PROJECT_SOURCE_DIR}/distribution)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${PROJECT_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${PROJECT_BINARY_DIR}/lib)
link_directories(${CMAKE_LIBRARY_OUTPUT_DIRECTORY})

include_directories(third_party/lua-5.4.7/include)

add_subdirectory(third_party)
add_subdirectory(src)
add_subdirectory(samples)

