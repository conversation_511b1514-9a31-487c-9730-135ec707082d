// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-04-30
//

#include "path/src/path_interface_impl.h"
#include "path/src/base/uuid.h"

#include <algorithm>

namespace aurora {
namespace path {

PathInterfaceImpl::PathInterfaceImpl() {
    // Initialize the path thread
    path_thread_ = std::make_unique<LoopThread>("PathCalculationThread");
    // route_algorithm_ = std::make_shared<BidirectionalAStar>();
    // route_algorithm_ = std::make_shared<Dijkstra>();
}

int PathInterfaceImpl::Prepare(const std::string& config) {
    // Load configuration from YAML file
    // std::string config_file = "src/path/config/path.yaml";
    // if (!config.empty()) {
    //     config_file = config;
    // }

    // Initialize the configuration manager with the config file
    if (!PathConfigManager::Initialize(config)) {
        LOG_ERROR("Failed to load configuration file: {}", config);
        return static_cast<int>(ErrorCode::kErrorCodeFailed);;
    }

    LOG_INFO("Successfully loaded configuration from: {}", config);

    // Initialize the route algorithm based on configuration
    std::string algorithm_type = PathConfigManager::Instance().GetRouteAlgorithm();
    if (algorithm_type == "dijkstra") {
        route_algorithm_ = std::make_shared<Dijkstra>();
    } else if (algorithm_type == "bidirectional_astar") {
        route_algorithm_ = std::make_shared<BidirectionalAStar>();
    } else {
        LOG_WARN("Unknown route algorithm: {}, using Dijkstra as default", algorithm_type);
        route_algorithm_ = std::make_shared<Dijkstra>();
    }

    return 0;
}

int32_t PathInterfaceImpl::SetParams(const ParameterMap &parameters) {
    // get value
    try {
        auto dir_pth = std::get<std::string>(parameters.at("data_dir"));
        LOG_INFO("dir_path:{}", dir_pth);
    } catch (const std::bad_variant_access&) {
        // return default value
        LOG_ERROR("dir_path ser error");
    }

    PathConfigManager::Instance().SetParams(parameters);
    return 0;
}

std::shared_ptr<IInterface> PathInterfaceImpl::GetInterface() {
    return shared_from_this();
}

int PathInterfaceImpl::Init(InterfaceFinder finder) {
    // TODO: Implement initialization logic
    // step1.get map_matching interface (optional for now)
    auto map_matching_interface = finder(ModuleId::kModuleIdLocation);
    if (!map_matching_interface) {
        // Location module is optional for now, just log a warning
        LOG_WARN("Location module not found, map matching will not be available");
    } else {
        // registor mapmatching callback
        // map_matching_interface->RegistMapMatchingCallback(
        //     std::bind(&PathModuleImpl::OnMapMatchingCallback, this, std::placeholders::_1));
    }
    return 0;
}

int PathInterfaceImpl::Start() {
    // Start the path thread
    if (path_thread_) {
        path_thread_->Start();
    }
    return 0;
}

int PathInterfaceImpl::Stop() {
    // Stop the path thread
    if (path_thread_) {
        path_thread_->Stop();
    }
    return 0;
}

int PathInterfaceImpl::UnInit() {
    listeners_.clear();
    return 0;
}

ModuleId PathInterfaceImpl::GetModuleId() const {
    return ModuleId::kModuleIdPath;
}

std::string PathInterfaceImpl::RequestPath(const PathQueryPtr& query) {
    // Generate a UUID for this request
    std::string uuid = Uuid::GetUuid();

    // todo: get ego_pt from matching module
    if (query == nullptr || query->path_points.size() < 2) {
        LOG_ERROR("Path query must have at least 2 path_points");
        // constrcut PathResult and send to listener
        auto result = std::make_shared<PathResult>();
        result->uuid = uuid;
        result->status = "error";
        result->code = static_cast<int>(ErrorCode::kErrorCodeFailed);
        for (const auto& listener : listeners_) {
            listener->OnPathResult(query, result);
        }
        return uuid;
    }

    // Create a copy of the query for the thread
    PathQuery query_new = *query;

    // Post the path calculation task to the thread
    path_thread_->Post([this, query_new, uuid]() {
        // Execute path calculation in the background thread
        route_algorithm_->set_interrupt(nullptr);
        route_algorithm_->Clear();
        auto paths = route_algorithm_->CalcRoute(query_new);

        // erase if links.empty()
        paths.erase(
            std::remove_if(paths.begin(), paths.end(),
                [](const PathInfo& path) { return path.links.empty(); }),
            paths.end());

        // Form PathResult and call listeners
        auto result = std::make_shared<PathResult>();
        result->uuid = uuid;
        result->status = paths.empty() ? "error" : "ok";
        result->tag = paths.empty() ? "highway fist" : "distance first";
        result->code = paths.empty() ? static_cast<int>(ErrorCode::kErrorCodePathOfflineError) : 0;
        result->paths = paths;

        auto query_ptr = std::make_shared<PathQuery>(query_new);
        if (PathConfigManager::Instance().IsDebugEnabled()) {
            std::string path_query_file = PathConfigManager::Instance().GetDebugOutPath() + "/path_query.geojson";
            GeoJsonWritter::WritePathQuery(query_ptr, path_query_file);
            std::string path_result_file = PathConfigManager::Instance().GetDebugOutPath() + "/path_result.geojson";
            GeoJsonWritter::WritePathResult(*result, path_result_file);
        }
        LOG_INFO("send path result, uuid: {}, status: {}, code: {}, paths size: {}", result->uuid, result->status, result->code, result->paths.size());

        // Call listeners with the result
        for (const auto& listener : listeners_) {
            listener->OnPathResult(query_ptr, result);
        }
    });

    // Return the UUID immediately so the caller can track the request
    return uuid;
}

int32_t PathInterfaceImpl::CancelRequest(const std::string& uuid) {
    return 0;
}

bool PathInterfaceImpl::AddPathListener(const PathListenerPtr& listener) {
    if (listener == nullptr) {
        return false;
    }
    listeners_.push_back(listener);
    return true;
}

bool PathInterfaceImpl::RemovePathListener(const PathListenerPtr& listener) {
    if (listener == nullptr) {
        return false;
    }
    auto it = std::find(listeners_.begin(), listeners_.end(), listener);
    if (it != listeners_.end()) {
        listeners_.erase(it);
        return true;
    }
    return false;
}

int32_t PathInterfaceImpl::DoLandMarkMatching(PathLandmark& landmark) {
    // TODO: Implement landmark matching logic
    if (!landmark.valid) {
        return -1;
    }

    // TODO: Implement landmark matching logic
    return 0;
}

}  // namespace path
}  // namespace aurora
