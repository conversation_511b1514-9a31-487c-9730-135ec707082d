// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-05-15
//
#include <vector>
#include <limits>
#include "path_module.h"
#include "route_algorithm.h"
#include "dijkstra.h"
#include "logger.h"

namespace aurora {
namespace path {

// Invalid label value
// constexpr uint32_t kInvalidLabel = std::numeric_limits<uint32_t>::max();

// Bucket count for the adjacency list
constexpr uint32_t kBucketCount = 20000;
const std::string kMaxReservedLableCntKey = "algorithms.dijkstra.max_reserved_labels_count";

Dijkstra::Dijkstra() {

    max_reserved_labels_count_ = 1000000;
    // Get configuration values from PathConfig
    const auto& config = PathConfigManager::Instance();
    
    // Set max reserved labels count from configuration
    max_reserved_labels_count_ = config.Get(kMaxReservedLableCntKey, 1000000);
    
    // Initialize graph reader
    graph_reader_ptr_ = std::make_shared<GraphReader>();
    
    // Load tile data using center point and match radius from configuration
    PointLL center_pt = config.GetCenterPoint();
    double match_radius = config.GetMatchRadius() / 1000.0; // Convert to appropriate units
    graph_reader_ptr_->LoadTileData(center_pt, 0, match_radius);
}

Dijkstra::~Dijkstra() = default;

std::vector<PathInfo> Dijkstra::CalcRoute(const PathQuery& query) {
    LOG_INFO("Dijkstra::CalcRoute");
    ScopeTime scope_time("CalcRoute");
    query_ = query;
    auto start_landmark = query.path_points.front();
    auto end_landmark = query.path_points.back();
    // Initialize the algorithm
    Init(start_landmark->pt, end_landmark->pt);
    
    // Set origin and destination
    SetOrigin(start_landmark);
    SetDestination(end_landmark);
    
    // Main Dijkstra loop
    uint32_t pred_idx = 0;
    EdgeLabel pred;
    
    while (true) {
        // Get the next edge from the adjacency list
        pred_idx = adjacencylist_.pop();
        
        // Check if the queue is empty
        if (pred_idx == kInvalidLabel) {
            LOG_INFO("Dijkstra: No path found - adjacency list is empty");
            return {};
        }

        // Get the edge label
        pred = edgelabels_[pred_idx];

        // if ((edgelabels_.size() % 5000) == 1) {
        //     WriteVisitedEdges(std::to_string(pred_idx), edgelabels_);
        // }

        if (edgelabels_.size() % 10000 == 0) {
            LOG_INFO("Dijkstra: Visited {} edges", edgelabels_.size());
        }

        if (pred.cost().cost > cost_threshold_) {
            LOG_INFO("Dijkstra: Cost threshold exceeded");
            return FormPath();
        }

        // Set edge status to permanent
        // edge_status_.set_permanent(pred.edgeid());

        edge_status_.Update(pred.graphid(), pred.forward(), EdgeSet::kPermanent);
        
        // Check if we've reached the destination
        bool destination_found = false;
        for (const auto& candidate : destination_->candidates) {
            if (pred.graphid() == GraphId(candidate.link->tile_id, candidate.link->id)) {
                destination_found = true;
                // todo: check direction
                best_edgelabel_ = pred;
                best_idx_ = pred_idx;

                GraphId edge_id = {candidate.link->tile_id, candidate.link->id};
                auto edgePtr = graph_reader_ptr_->GetDirectEdgeInfo(edge_id);
        
                Cost edge_cost = costing_->EdgeCost(edgePtr);
                if (pred.forward()) {
                    edge_cost = edge_cost * (candidate.offset / candidate.link->length);
                } else {
                    edge_cost = edge_cost * (1.0f - (candidate.offset / candidate.link->length));
                }
                // best_connection_.push_back({pred_idx, pred.cost().cost, pred.forward()});
                float c =0.0f;
                if (pred.predecessor() != kInvalidLabel) {
                    c = edgelabels_[pred.predecessor()].cost().cost + edge_cost.cost;
                    best_connection_.push_back({pred_idx, c, pred.forward()});
                    cost_threshold_ = c + 200;
                    if (c < best_connection_.front().cost) {
                        std::swap(best_connection_.front(), best_connection_.back());
                    }
                }


                break;
            }
        }
        
        // if (destination_found) {
        //     LOG_INFO("Dijkstra: Destination found");
        //     return FormPath();
        // }
        
        // Check if we've exceeded the iteration threshold
        if (edgelabels_.size() > iterations_threshold_) {
            LOG_INFO("Dijkstra: Exceeded iteration threshold");
            return {};
        }

        // Expand from the current edge
        Expand(pred.graphid(), pred, pred_idx);
    }
    
    return {};
}

void Dijkstra::Clear() {
    edgelabels_.clear();
    adjacencylist_.clear();
    edge_status_.clear();
}

void Dijkstra::Init(const PointLL& orig, const PointLL& dest) {
    // Create costing object
    Costing costing;
    costing_ = std::make_shared<AutoCost>(costing, graph_reader_ptr_);

    heuristic_cost_ = std::make_shared<AStarHeuristic>();
    heuristic_cost_->Init(dest);
    
    // Reserve space for edge labels
    edgelabels_.reserve(max_reserved_labels_count_);
    
    // Initialize the adjacency list
    const float mincost = heuristic_cost_->Get(orig);
    const float range = 1000000.0f;
    const uint32_t bucketsize = 1;
    adjacencylist_.reuse(mincost, range, bucketsize, &edgelabels_);
    
    // Set iteration threshold
    iterations_threshold_ = std::numeric_limits<uint32_t>::max();
    cost_threshold_ = std::numeric_limits<float>::max();
    best_connection_.clear();
}

void Dijkstra::SetOrigin(const PathLandmarkPtr& origin) {
    ScopeTime scope_time("SetOrigin");
    if (!origin || !origin->valid) {
        LOG_INFO("Origin is invalid");
        return;
    }

    graph_reader_ptr_->StaticMatching(origin);
    origin_ = origin;

    if (origin->candidates.empty()) {
        throw std::runtime_error("Origin is invalid");
        return;
    }

    // Add each origin candidate to the adjacency list
    for (const auto& candidate : origin->candidates) {
        // Calculate the cost from the origin to the edge
        GraphId edge_id = {candidate.link->tile_id, candidate.link->id};
        auto edgePtr = graph_reader_ptr_->GetDirectEdgeInfo(edge_id);

        Cost edge_cost = costing_->EdgeCost(edgePtr);
        Cost node_cost{};

        // if (edge_ptr can forward)
        if (edgePtr->is_forward()) {
            Cost cost = edge_cost *  (1.0f - (candidate.offset / candidate.link->length));

            // In A*, the sort cost is  the cost + heuristic (with heuristic)
            float sortcost = cost.cost;

            auto node_ptr = graph_reader_ptr_->GetNodeInfo(GraphId(edgePtr->id.tileid(), edgePtr->endnode_));
            sortcost += heuristic_cost_->Get(node_ptr->point);

            // Add to the edge labels
            uint32_t idx = edgelabels_.size();
            edge_status_.Set(edge_id, true, EdgeSet::kTemporary, idx);
            edgelabels_.emplace_back(kInvalidLabel, edge_id, edge_id.id(), edge_id.tileid(), cost, sortcost, node_cost, edgePtr, true);

            // Add to the adjacency list
            adjacencylist_.add(idx);
        }

        if (edgePtr->is_reverse()) {
            Cost cost = edge_cost *  (candidate.offset / candidate.link->length);

            // In A*, the sort cost is just the cost the cost + heuristic (with heuristic)
            float sortcost = cost.cost;
            auto node_ptr = graph_reader_ptr_->GetNodeInfo(GraphId(edgePtr->id.tileid(), edgePtr->startnode_));
            sortcost += heuristic_cost_->Get(node_ptr->point);

            // Add to the edge labels
            uint32_t idx = edgelabels_.size();
            edge_status_.Set(edge_id, false, EdgeSet::kTemporary, idx);
            edgelabels_.emplace_back(kInvalidLabel, edge_id, edge_id.id(), edge_id.tileid(), cost, sortcost, node_cost, edgePtr, false);

            // Add to the adjacency list
            adjacencylist_.add(idx);
        }
    }
}

void Dijkstra::SetDestination(const PathLandmarkPtr& destination) {
    // In Dijkstra, we don't need to do anything special for the destination
    // We'll check for the destination during the main loop
    ScopeTime scope_time("SetDestination");
    if (!graph_reader_ptr_->StaticMatching(destination)) {
        LOG_INFO("Destination is invalid");
        throw std::runtime_error("Destination is invalid");
        return;
    }
    destination_ = destination;
}

void Dijkstra::Expand(GraphId id, EdgeLabel pred, uint32_t pred_idx) {
    // Get the directed edge
    auto edge = graph_reader_ptr_->GetDirectEdgeInfo(id);
    if (!edge) {
        LOG_INFO("Edge not found: {}", id.ToString());
        return;
    }
    
    // Get the end node of this edge
    // keneng 跨tile

    auto expand_node_id = pred.forward() ? edge->endnode_ : edge->startnode_;
    auto unique_node_id = GraphId(id.tileid(), expand_node_id);

    auto expandnode_ptr = graph_reader_ptr_->GetNodeInfo(unique_node_id);
    if (!expandnode_ptr) {
        LOG_INFO("End node not found: {}", static_cast<uint64_t>(edge->endnode_));
        return;
    }

    // get adjacent edges
    std::vector<DirectEdgeInfoPtr> adj_out_edges;
    if (!expandnode_ptr->boundary_node_ids.empty()) {
        for (const auto& adj_node_id : expandnode_ptr->boundary_node_ids) {
            auto adj_node_ptr = graph_reader_ptr_->GetNodeInfo(adj_node_id);
            if (!adj_node_ptr) {
                continue;
            }
            graph_reader_ptr_->GetExitEdges(adj_node_ptr, adj_out_edges);
        }
    }

    std::vector<DirectEdgeInfoPtr> out_edges;
    graph_reader_ptr_->GetExitEdges(expandnode_ptr, out_edges);
    out_edges.insert(out_edges.end(), adj_out_edges.begin(), adj_out_edges.end());

    const auto is_node_match = [&expandnode_ptr, &unique_node_id](GraphId node_id) {
        // return forward ? edge->startnode_ == expand_node_id : edge->endnode_ == expand_node_id;
        if (node_id == unique_node_id) {
            return true;
        }

        return std::find(expandnode_ptr->boundary_node_ids.begin(), expandnode_ptr->boundary_node_ids.end(), node_id)
            != expandnode_ptr->boundary_node_ids.end();
    };
    
    // Expand to all outgoing edges from this node, get outgong_edges
    for (const auto& outedge : out_edges) {
        if (outedge->id == id) {
            // todo: not uturn
            continue;
        }

        // Get the directed edge
        // auto directededge = graph_reader_ptr_->GetDirectEdgeInfo({id.tileid(), outedge_id});
        // if (!directededge) {
        //     continue;
        // }

        if (outedge->is_forward() && is_node_match(GraphId(outedge->id.tileid(), outedge->startnode_))) {
          // Skip if this edge is already permanently labeled
          if (edge_status_.Get(outedge->id, true).set() == EdgeSet::kPermanent) {
            continue;
          }

          // Check if we should prune this edge based on costing
          if (!costing_->Allowed(pred, expandnode_ptr, outedge)) {
            continue;
          }

          // Get cost for traversing this edge
          Cost edgecost = costing_->EdgeCost(outedge);
          Cost nodecost = costing_->NodeCost(pred, expandnode_ptr, outedge);

          // Get the cost from the previous edge
          Cost newcost = pred.cost() + edgecost;

          // Check if this is a new edge or a better path to an existing edge
          EdgeStatusInfo status = edge_status_.Get(outedge->id, true);
          if (status.set() == EdgeSet::kTemporary) {
            uint32_t idx = status.index();
            if (newcost.cost < edgelabels_[idx].cost().cost) {
              float newsortcost = pred.sortcost() - (pred.cost().cost - newcost.cost);
              // Update the adjacency list
              adjacencylist_.decrease(idx, newsortcost);
                            // Update the edge label with the new cost
              //edgelabels_.emplace_back(kInvalidLabel, edge_id, edge_id.id(), edge_id.tileid(), cost, sortcost, true);

              edgelabels_[idx] = EdgeLabel(pred_idx, outedge->id,
                outedge->id.id(), outedge->id.tileid(), newcost, newsortcost, nodecost, outedge, true);
            }
          } else {
            // Add a new edge label
            auto node_ptr = graph_reader_ptr_->GetNodeInfo(GraphId(outedge->id.tileid(), outedge->endnode_));
            float sortcost = newcost.cost + heuristic_cost_->Get(node_ptr->point);
            uint32_t idx = edgelabels_.size();
            edgelabels_.emplace_back(pred_idx, outedge->id, outedge->id.id(), outedge->id.tileid(), newcost,
                                     sortcost, nodecost, outedge, true);

            // Add to the adjacency list and edge status
            adjacencylist_.add(idx);
            edge_status_.Set(outedge->id, true, EdgeSet::kTemporary, idx);
          }
        }

        if (outedge->is_reverse() && is_node_match(GraphId(outedge->id.tileid(), outedge->endnode_))) {
          // Skip if this edge is already permanently labeled
          if (edge_status_.Get(outedge->id, false).set() == EdgeSet::kPermanent) {
            continue;
          }

          // Check if we should prune this edge based on costing
          if (!costing_->Allowed(pred, expandnode_ptr, outedge)) {
            continue;
          }

          // Get cost for traversing this edge
          Cost edgecost = costing_->EdgeCost(outedge);
          Cost nodecost = costing_->NodeCost(pred, expandnode_ptr, outedge);

          // Get the cost from the previous edge
          Cost newcost = pred.cost() + edgecost;

          // Check if this is a new edge or a better path to an existing edge
          EdgeStatusInfo status = edge_status_.Get(outedge->id, false);
          if (status.set() == EdgeSet::kTemporary) {
            uint32_t idx = status.index();
            if (newcost.cost < edgelabels_[idx].cost().cost) {
              float newsortcost = pred.sortcost() - (pred.cost().cost - newcost.cost);
              // Update the adjacency list
              adjacencylist_.decrease(idx, newsortcost);
                            // Update the edge label with the new cost
              //edgelabels_.emplace_back(kInvalidLabel, edge_id, edge_id.id(), edge_id.tileid(), cost, sortcost, true);

              edgelabels_[idx] = EdgeLabel(pred_idx, outedge->id,
                outedge->id.id(), outedge->id.tileid(), newcost, newsortcost, nodecost, outedge, false);
            }
          } else {
            // Add a new edge label
            auto node_ptr = graph_reader_ptr_->GetNodeInfo(GraphId(outedge->id.tileid(), outedge->startnode_));
            float sortcost = newcost.cost + heuristic_cost_->Get(node_ptr->point);
            uint32_t idx = edgelabels_.size();
            edgelabels_.emplace_back(pred_idx, outedge->id, outedge->id.id(), outedge->id.tileid(), newcost,
                                     sortcost, nodecost,outedge, false);

            // Add to the adjacency list and edge status
            adjacencylist_.add(idx);
            edge_status_.Set(outedge->id, false, EdgeSet::kTemporary, idx);
          }
        }
    }
}

std::vector<PathInfo> Dijkstra::FormPath() {
    // Find the destination edge
    if (best_connection_.empty()) {
        LOG_INFO("Dijkstra: Could not find destination edge in edge labels");
        return {};
    }

    WriteVisitedEdges("final", edgelabels_);
    // uint32_t dest_idx = best_idx_;
    uint32_t dest_idx = best_connection_.back().lable_index;
    bool found = true;

    // for (uint32_t i = 0; i < edgelabels_.size(); i++) {
    //     for (const auto& candidate : destination_->candidates) {
    //         if (edgelabels_[i].edgeid() == candidate.link->id.value && 
    //             edge_status_.get(candidate.link->id.value).set() == EdgeSet::kPermanent) {
    //             dest_idx = i;
    //             found = true;
    //             break;
    //         }
    //     }
    //     if (found) break;
    // }

    // if (!found) {
    //     LOG_INFO("Dijkstra: Could not find destination edge in edge labels");
    //     return {};
    // }

    // Reverse trace the path from destination to origin
    std::vector<EdgeInfoPtr> path_edges;
    std::vector<DirectEdgeInfoPtr> direct_edges;
    std::vector<PointLL> path_points;
    double path_length = 0.0;
    double path_time = 0.0;
    double start_offset = 0.0f;
    double end_offset = 0.0f;
    
    // Start with the destination edge
    uint32_t idx = dest_idx;
    while (idx != kInvalidLabel) {
        const EdgeLabel& edge_label = edgelabels_[idx];
        
        // Get the edge info
        auto direct_edge = edge_label.edge_ptr(); //graph_reader_ptr_->GetDirectEdgeInfo(edge_label.edgeid());
        direct_edges.push_back(direct_edge);
        auto edge = std::make_shared<EdgeInfo>();
        if (edge) {
            edge->tile_id = direct_edge->id.tileid();
            edge->id = direct_edge->id.local_id;
            edge->forward = edge_label.forward();
            edge->is_inner_link = direct_edge->is_inner_edge;
            edge->length = direct_edge->length_;
            edge->geos = direct_edge->geos;

            // start landmark process
            bool is_start_landmark = false;
            for (const auto& candidate : origin_->candidates) {
                if (direct_edge->id == GraphId(candidate.link->tile_id, candidate.link->id)) {
                  std::vector<PointLL> used_points;
                  if (edge_label.forward()) {
                    used_points.push_back(candidate.proj_pt);
                    for (size_t i = candidate.proj_index + 1;
                         i < direct_edge->geos.size(); i++) {
                      used_points.push_back(direct_edge->geos[i]);
                    }
                    std::reverse(used_points.begin(), used_points.end());
                    path_length += std::fmax(0.0f, edge->length - candidate.offset);  // use offset
                    start_offset = candidate.offset;
                  } else {
                    for (size_t i = 0; i <= candidate.proj_index; i++) {
                      used_points.push_back(direct_edge->geos[i]);
                    }
                    used_points.push_back(candidate.proj_pt);
                    path_length += std::fmin(0.0f, candidate.offset);  // use offset
                  }
                  is_start_landmark = true;
                  path_points.insert(path_points.end(), used_points.begin(), used_points.end());
                  start_offset = std::fmax(0.0f, edge->length - candidate.offset);
                  break;
                }
            }

            // end lanemark process
            bool is_end_landmark = false;
            for (const auto& candidate : destination_->candidates) {
                if (direct_edge->id == GraphId(candidate.link->tile_id, candidate.link->id)) {
                    std::vector<PointLL> used_points;
                    if (edge_label.forward()) {
                        for (size_t i = 0; i <= candidate.proj_index; i++) {
                            used_points.push_back(direct_edge->geos[i]);
                        }
                        used_points.push_back(candidate.proj_pt);
                        std::reverse(used_points.begin(), used_points.end());
                        path_length += candidate.offset;  // use offset
                        end_offset = std::fmax(0.0f, edge->length - candidate.offset);
                    } else {
                        used_points.push_back(candidate.proj_pt);
                        for (size_t i = candidate.proj_index + 1; i < direct_edge->geos.size(); i++) {
                            used_points.push_back(direct_edge->geos[i]);
                        }
                        path_length += std::fmax(0.0f, edge->length - candidate.offset);  // use offset
                        end_offset = candidate.offset;
                    }
                    is_end_landmark = true;
                    path_points.insert(path_points.end(), used_points.begin(), used_points.end());
                    break;
                }
            }

            if (!is_start_landmark && !is_end_landmark) {
                std::vector<PointLL> geo_points = edge->geos;
                if (edge_label.forward()) {
                    std::reverse(geo_points.begin(), geo_points.end());
                }
                path_points.insert(path_points.end(), geo_points.begin(), geo_points.end());
                // Add to the path length and time
                path_length += edge->length;  // use offset
            }

            // Add the edge to the path
            path_edges.push_back(edge);
            
            // Add the edge points to the path
            path_time += edge_label.cost().secs;
        }
        // Move to the predecessor
        idx = edge_label.predecessor();
    }

    // Reverse the path since we traced from destination to origin
    std::reverse(path_edges.begin(), path_edges.end());
    std::reverse(path_points.begin(), path_points.end());

    // debug 
    auto path_edge_ptr = std::make_shared<DirectEdgeInfo>();
    path_edge_ptr->id = GraphId(1, 1);
    path_edge_ptr->geos = path_points;

    if (PathConfigManager::Instance().IsDebugEnabled()) {
        std::string path_file = PathConfigManager::Instance().GetDebugOutPath() + "/dijkstra_path.geojson";
        geojson_writter_.WriteDirectEdgeInfo(path_edge_ptr, path_file);
    }

    // Create the path info
    PathInfo path_info;
    path_info.path_id = 1;  // Assign a unique ID
    path_info.length = path_length;
    // path_info.travel_time = path_time;
    path_info.travel_time = path_info.length * 3.6f / 60.0f;
    path_info.links = path_edges;
    path_info.points = path_points;
    
    // Create sections for the path
    // uint32_t section_idx = 0;
    // for (const auto& edge : path_edges) {
    //     Section section;
    //     section.index = section_idx++;
    //     section.num = 1;
    //     section.length = edge->length;
    //     section.time = edge->length / (edge->positive_speed_limit * 5.0);  // Estimate time based on speed limit
    //     section.start_offset = 0.0;
    //     section.end_offset = edge->length;

    //     path_info.sections.push_back(section);
    // }

    {
        Section section;
        section.index = 0;
        section.num = path_edges.size();
        section.length = path_info.length;
        section.time = path_info.travel_time;
        // TODO: offset
        section.start_offset = origin_->candidates.front().offset;
        section.end_offset = destination_->candidates.front().offset;
        path_info.sections.push_back(section);
    }

    // Calculate the bounding box
    if (!path_points.empty()) {
        // AABB2<PointLL> bbox;
        // for (const auto& pt : path_points) {
        //     bbox.Expand(pt);
        // }
        path_info.bbox = AABB2<PointLL>(path_points);
    }

    if (PathConfigManager::Instance().IsDebugEnabled()) {
        std::string route_file = PathConfigManager::Instance().GetDebugOutPath() + "/route.geojson";
        geojson_writter_.WriteDirectEdgeInfos(direct_edges, route_file);
    }
    LOG_INFO("Dijkstra: Path found with length {}, links {}", path_length, path_edges.size());

    return {path_info};
}

void Dijkstra::WriteVisitedEdges(const std::string& filename, const std::vector<EdgeLabel>& edge_labels) {
    // Get debug output path from configuration
    const auto& config = PathConfigManager::Instance();
    std::string debug_out_path = config.GetDebugOutPath();
    
    // Only write debug output if debug is enabled
    if (!config.IsDebugEnabled()) {
        return;
    }

    // Construct output file path
    std::string out_file = debug_out_path + "/dijsktra_edgelabel_" + filename + ".geojson";
    
    // Write edge labels to file
    geojson_writter_.WriteEdgeLabels(edgelabels_, out_file);
}

}  // namespace path
}  // namespace aurora
