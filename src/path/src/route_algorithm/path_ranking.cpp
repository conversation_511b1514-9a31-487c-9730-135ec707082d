// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-06-10
//

#include "path_ranking.h"
#include <algorithm>
#include <unordered_map>
#include "logger.h"
#include "scope_time.h"

namespace aurora {
namespace path {

PathRanking::PathRanking(std::shared_ptr<GraphReader> graph_reader)
    : graph_reader_ptr_(graph_reader) {
}

std::vector<PathInfo> PathRanking::FindAlternativePaths(
    const std::vector<EdgeLabel>& forward_labels,
    const std::vector<EdgeLabel>& reverse_labels,
    const std::vector<CandidateConnection>& connections,
    const PathLandmarkPtr& origin,
    const PathLandmarkPtr& destination) {

    ScopeTime scope_time("FindAlternativePaths");

    // Check if we have any connections
    if (connections.empty()) {
        LOG_INFO("No connections found for alternative paths");
        return {};
    }

    // Use connections directly as candidates
    std::vector<CandidateConnection> candidates = connections;

    // Sort candidates by cost
    std::sort(candidates.begin(), candidates.end());

    // Collect edge IDs for each candidate
    // for (auto& candidate : candidates) {
    //     candidate.UpdatePathEdges(forward_labels, reverse_labels);
    // }

    // Select the best paths
    std::vector<CandidateConnection> best_paths;

    // Always include the best path
    if (!candidates.empty()) {
        best_paths.push_back(candidates[0]);

        // Try to add more paths if available
        for (size_t i = 1; i < candidates.size() && best_paths.size() < PathRankingConstants::kMaxAlternativePaths; ++i) {
            if (IsValidAlternative(candidates[i], best_paths)) {
                LOG_INFO("Adding alternative path with cost: {}, idx: {}", candidates[i].cost, i);
                best_paths.push_back(candidates[i]);
            }
        }
    }

    LOG_INFO("Selected {} alternative paths", best_paths.size());

    // Form the final paths
    std::vector<PathInfo> result;
    for (const auto& path : best_paths) {
        result.push_back(FormPath(path, forward_labels, reverse_labels, origin, destination));
    }

    return result;
}

PathInfo PathRanking::FormPath(
    const CandidateConnection& candidate,
    const std::vector<EdgeLabel>& forward_labels,
    const std::vector<EdgeLabel>& reverse_labels,
    const PathLandmarkPtr& origin,
    const PathLandmarkPtr& destination) {

    // Get the forward and reverse path edges
    std::vector<EdgeInfoPtr> path_edges;
    std::vector<DirectEdgeInfoPtr> direct_edges;
    std::vector<PointLL> path_points;
    double path_length = 0.0;
    double path_time = 0.0;
    double start_offset = 0.0f;
    double end_offset = 0.0f;

    // deal with single line

    if (candidate.edge_ids.size() == 1) {
        Candidate start_candi;
        Candidate end_candi;
        bool is_start_find = false;
        bool is_end_find = false;
        auto edge_ptr  = forward_labels[candidate.forward_index].edge_ptr();
        bool dir = forward_labels[candidate.forward_index].forward();

        for (const auto& cand : origin->candidates) {
            if (edge_ptr->id == GraphId(cand.link->tile_id, cand.link->id)) {
                start_candi = cand;
                is_start_find = true;
                break;
            }
        }

        for (const auto& cand : destination->candidates) {
            if (edge_ptr->id == GraphId(cand.link->tile_id, cand.link->id)) {
                end_candi = cand;
                is_end_find = true;
                break;
            }
        }

        if (is_start_find && is_end_find) {
            if (dir && end_candi.offset >= start_candi.offset) {
                // ok.
                std::vector<PointLL> path_points;
                path_points.push_back(start_candi.proj_pt);

                for (size_t i = start_candi.proj_index + 1; i < end_candi.proj_index + 1; i++) {
                    path_points.push_back(edge_ptr->geos[i]);
                }
                path_points.push_back(end_candi.proj_pt);

                auto edge = std::make_shared<EdgeInfo>();
                edge->tile_id = edge_ptr->id.tileid();
                edge->id = edge_ptr->id.local_id;
                edge->forward = true;
                edge->is_inner_link = edge_ptr->is_inner_edge;
                edge->length = edge_ptr->length_;
                edge->geos = edge_ptr->geos;

                // Create the path info
                PathInfo path_info;
                path_info.path_id = static_cast<uint64_t>(std::hash<double>{}(candidate.cost));  // Generate a unique ID
                path_info.length = end_candi.offset - start_candi.offset;
                path_info.travel_time =
                    path_info.length * 3.6f / 60.0f;  // Use cost as travel time
                path_info.links = {edge};
                path_info.points = path_points;

                // Create sections
                Section section;
                section.index = 0;
                section.num = 1;
                section.length = path_length;
                section.time = candidate.cost;
                section.start_offset = start_candi.offset;
                section.end_offset = end_candi.offset;
                path_info.sections.push_back(section);

                // Calculate bounding box
                if (!path_points.empty()) {
                  path_info.bbox = AABB2<PointLL>(path_points);
                }
                return path_info;
            }
            if (!dir && end_candi.offset <= start_candi.offset) {
                // ok construct result.
                std::vector<PointLL> path_points;
                path_points.push_back(end_candi.proj_pt);

                for (size_t i = end_candi.proj_index + 1; i < start_candi.proj_index + 1; i++) {
                    path_points.push_back(edge_ptr->geos[i]);
                }
                path_points.push_back(start_candi.proj_pt);
                std::reverse(path_points.begin(), path_points.end());

                auto edge = std::make_shared<EdgeInfo>();
                edge->tile_id = edge_ptr->id.tileid();
                edge->id = edge_ptr->id.local_id;
                edge->forward = false;
                edge->is_inner_link = edge_ptr->is_inner_edge;
                edge->length = edge_ptr->length_;
                edge->geos = edge_ptr->geos;

                // Create the path info
                PathInfo path_info;
                path_info.path_id = static_cast<uint64_t>(std::hash<double>{}(candidate.cost));  // Generate a unique ID
                path_info.length = start_candi.offset - end_candi.offset;
                path_info.travel_time =
                    path_info.length * 3.6f / 60.0f;  // Use cost as travel time
                path_info.links = {edge};
                path_info.points = path_points;

                // Create sections
                Section section;
                section.index = 0;
                section.num = 1;
                section.length = path_length;
                section.time = candidate.cost;
                section.start_offset = std::fmax(0.0f, edge->length -  start_candi.offset);//  start_offset;
                section.end_offset = std::fmax(0.0f, edge->length - end_candi.offset); //end_offset;
                path_info.sections.push_back(section);

                // Calculate bounding box
                if (!path_points.empty()) {
                  path_info.bbox = AABB2<PointLL>(path_points);
                }
                return path_info;
            }
        }
        // throw std::runtime_error("FormPath error"); 
        LOG_INFO("search error....");
        PathInfo info;
        return info;
    }

    // Start with the forward path
    uint32_t idx = candidate.forward_index;
    while (idx != kInvalidLabel) {
        const EdgeLabel& edge_label = forward_labels[idx];

        // Get the edge info
        auto direct_edge = edge_label.edge_ptr();
        if (direct_edge) {
            direct_edges.push_back(direct_edge);
            auto edge = std::make_shared<EdgeInfo>();
            edge->tile_id = direct_edge->id.tileid();
            edge->id = direct_edge->id.local_id;
            edge->forward = edge_label.forward();
            edge->is_inner_link = direct_edge->is_inner_edge;
            edge->length = direct_edge->length_;
            edge->geos = direct_edge->geos;
            // LOG_INFO("Forward tile_Id: {}, edge: {}, idx:{}, prev: {}", edge->tile_id, edge->id, idx, edge_label.predecessor());

            // Check if this is the origin edge
            bool is_origin_edge = false;
            for (const auto& cand : origin->candidates) {
                if (direct_edge->id == GraphId(cand.link->tile_id, cand.link->id)) {
                    std::vector<PointLL> used_points;
                    if (edge_label.forward()) {
                        used_points.push_back(cand.proj_pt);
                        for (size_t i = cand.proj_index + 1; i < direct_edge->geos.size(); i++) {
                            used_points.push_back(direct_edge->geos[i]);
                        }
                        std::reverse(used_points.begin(), used_points.end());
                        path_length += std::fmax(0.0f, edge->length - cand.offset);
                        start_offset = cand.offset;
                    } else {
                        for (size_t i = 0; i <= cand.proj_index; i++) {
                            used_points.push_back(direct_edge->geos[i]);
                        }
                        used_points.push_back(cand.proj_pt);
                        path_length += cand.offset;
                        start_offset = std::fmax(0.0f, edge->length - cand.offset);
                    }
                    is_origin_edge = true;
                    path_points.insert(path_points.end(), used_points.begin(), used_points.end());
                    break;
                }
            }

            // If not the origin edge, add the full edge
            if (!is_origin_edge) {
                std::vector<PointLL> used_points = direct_edge->geos;
                if (edge_label.forward()) {
                    std::reverse(used_points.begin(), used_points.end());
                }
                path_points.insert(path_points.end(), used_points.begin(), used_points.end());
                path_length += edge->length;
            }

            path_edges.push_back(edge);
        }

        // Move to the next edge
        idx = edge_label.predecessor();
    }

    // Reverse the path edges since we built it from destination to origin
    std::reverse(path_edges.begin(), path_edges.end());
    std::reverse(path_points.begin(), path_points.end());

    // Now add the reverse path
    idx = candidate.reverse_index;
    if (idx == kInvalidLabel) {
        throw std::runtime_error("reverse_index is invalid"); 
    }
    idx = reverse_labels[idx].predecessor();
    while (idx != kInvalidLabel) {
        const EdgeLabel& edge_label = reverse_labels[idx];

        // Get the edge info
        auto direct_edge = edge_label.edge_ptr();
        if (direct_edge) {
            direct_edges.push_back(direct_edge);
            auto edge = std::make_shared<EdgeInfo>();
            edge->tile_id = direct_edge->id.tileid();
            edge->id = direct_edge->id.local_id;
            edge->length = direct_edge->length_;
            edge->forward = edge_label.forward();
            edge->is_inner_link = direct_edge->is_inner_edge;
            edge->geos = direct_edge->geos;
            // LOG_INFO("Reverse tile_Id: {}, edge: {}, idx:{}, prev: {}", edge->tile_id, edge->id, idx, edge_label.predecessor());

            // Check if this is the destination edge
            bool is_dest_edge = false;
            for (const auto& cand : destination->candidates) {
                if (direct_edge->id == GraphId(cand.link->tile_id, cand.link->id)) {
                    std::vector<PointLL> used_points;
                    if (edge_label.forward()) {
                        for (size_t i = 0; i <= cand.proj_index; i++) {
                            used_points.push_back(direct_edge->geos[i]);
                        }
                        used_points.push_back(cand.proj_pt);
                        path_length += cand.offset;
                        end_offset = cand.offset;
                    } else {
                        used_points.push_back(cand.proj_pt);
                        for (size_t i = cand.proj_index + 1; i < direct_edge->geos.size(); i++) {
                            used_points.push_back(direct_edge->geos[i]);
                        }
                        std::reverse(used_points.begin(), used_points.end());
                        path_length += std::fmax(0.0f, edge->length - cand.offset);
                        end_offset = std::fmax(0.0f, edge->length - cand.offset);
                    }
                    is_dest_edge = true;
                    path_points.insert(path_points.end(), used_points.begin(), used_points.end());
                    break;
                }
            }

            // If not the destination edge, add the full edge
            if (!is_dest_edge) {
                std::vector<PointLL> used_points = direct_edge->geos;
                if (!edge_label.forward()) {
                    std::reverse(used_points.begin(), used_points.end());
                }
                path_points.insert(path_points.end(), used_points.begin(), used_points.end());
                path_length += edge->length;
            }

            path_edges.push_back(edge);
        }

        // Move to the next edge
        idx = edge_label.predecessor();
    }

    // Create the path info
    PathInfo path_info;
    path_info.path_id = static_cast<uint64_t>(std::hash<double>{}(candidate.cost)); // Generate a unique ID
    path_info.length = path_length;
    path_info.travel_time = path_info.length * 3.6f / 60.0f; // Use cost as travel time
    path_info.links = path_edges;
    for (const auto& edge : path_edges) {
        LOG_INFO("path ranking: final edge id: {}", edge->id);
    }
    path_info.points = path_points;

    // Create sections
    Section section;
    section.index = 0;
    section.num = path_edges.size();
    section.length = path_length;
    section.time = candidate.cost;
    section.start_offset = start_offset;
    section.end_offset = end_offset;
    path_info.sections.push_back(section);

    // Calculate bounding box
    if (!path_points.empty()) {
        path_info.bbox = AABB2<PointLL>(path_points);
    }

    return path_info;
}

bool PathRanking::IsValidAlternative(
    const CandidateConnection& candidate,
    const std::vector<CandidateConnection>& best_paths) {

    // Check if the cost is within the allowed ratio
    if (candidate.cost > best_paths[0].cost * PathRankingConstants::kAtMostLonger) {
        LOG_INFO("Path rejected: cost ratio exceeded ({} > {})",
                 candidate.cost / best_paths[0].cost, PathRankingConstants::kAtMostLonger);
        return false;
    }

    // Check shared ratio with all existing best paths
    for (const auto& best_path : best_paths) {
        float shared_ratio = CalculateSharedRatio(candidate, best_path);
        if (shared_ratio > PathRankingConstants::kAtMostShared) {
            // LOG_INFO("Path rejected: shared ratio exceeded ({} > {})",
            //          shared_ratio, PathRankingConstants::kAtMostShared);
            return false;
        } else {
            // LOG_INFO("Path accepted: shared ratio within limit ({} <= {})",
            //          shared_ratio, PathRankingConstants::kAtMostShared);
        }
    }

    return true;
}

float PathRanking::CalculateSharedRatio(
    const CandidateConnection& path1,
    const CandidateConnection& path2) {

    // Count shared edges
    size_t shared_count = 0;
    std::vector<GraphId> common_ids;
    for (const auto& edge_id : path1.edge_ids) {
        if (path2.edge_ids.find(edge_id) != path2.edge_ids.end()) {
            shared_count++;
            common_ids.push_back(edge_id);
        }
    }

    float common_length = 0.0f;
    for (const auto& edge_id : common_ids) {
        auto edge = graph_reader_ptr_->GetDirectEdgeInfo(edge_id);
        common_length += edge->length_;
    }

    float total_length = 0.01f;
    for (const auto& edge_id : path2.edge_ids) {
        auto edge = graph_reader_ptr_->GetDirectEdgeInfo(edge_id);
        total_length += edge->length_;
    }
    // LOG_INFO("common_length: {}, total_length: {}, shared_count: {}, total_cnt: {}", common_length, total_length, shared_count, path2.edge_ids.size());
    return common_length / total_length;

    // Calculate ratio
    // size_t min_size = std::min(path1.edge_ids.size(), path2.edge_ids.size());
    // return min_size > 0 ? static_cast<float>(shared_count) / min_size : 0.0f;
}

void PathRanking::CollectPathEdgeIds(
    CandidateConnection& candidate,
    const std::vector<EdgeLabel>& forward_labels,
    const std::vector<EdgeLabel>& reverse_labels) {
    // Use the UpdatePathEdges method from CandidateConnection
    candidate.UpdatePathEdges(forward_labels, reverse_labels);
}

}  // namespace path
}  // namespace aurora
