// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-04-30
//

#ifndef PATH_SRC_ROUTE_ALGORITHM_CANDIDATE_CONNECTION_H_
#define PATH_SRC_ROUTE_ALGORITHM_CANDIDATE_CONNECTION_H_

#include <unordered_set>
#include "graphid.h"

namespace aurora {
namespace path {

struct CandidateConnection {
    GraphId edgeid;
    GraphId opp_edgeid;
    double cost;
    bool forward;  // Direction of the edge

    // Indices in the edge labels
    uint32_t forward_index;
    uint32_t reverse_index;

    // Edge IDs in the path
    std::unordered_set<GraphId> edge_ids;

    void UpdatePathEdges(const std::vector<EdgeLabel>& forward_labels, const std::vector<EdgeLabel>& reverse_labels) {
        // Collect forward path edge IDs
        uint32_t idx = forward_index;
        while (idx != kInvalidLabel) {
            const EdgeLabel& edge_label = forward_labels[idx];
            LOG_INFO("forward edge id: {}", edge_label.graphid().ToString());
            edge_ids.insert(edge_label.graphid());
            idx = edge_label.predecessor();
        }

        // Collect reverse path edge IDs
        idx = reverse_index;
        while (idx != kInvalidLabel) {
            const EdgeLabel& edge_label = reverse_labels[idx];
            LOG_INFO("reverse edge id: {}", edge_label.graphid().ToString());
            edge_ids.insert(edge_label.graphid());
            idx = edge_label.predecessor();
        }
    }

    bool operator<(const CandidateConnection& o) const {
        return cost < o.cost;
    }
    bool operator<(double c) const {
        return cost < c;
    }
};

}  // namespace path
}  // namespace aurora

#endif  // PATH_SRC_ROUTE_ALGORITHM_CANDIDATE_CONNECTION_H_ 