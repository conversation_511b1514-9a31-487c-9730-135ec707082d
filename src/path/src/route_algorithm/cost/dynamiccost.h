#ifndef AURORA_PATH_DYNAMICCOST_H_
#define AURORA_PATH_DYNAMICCOST_H_

#include <cstdint>
#include <memory>
#include <iostream>
#include <unordered_map>
#include <vector>
#include <array>
#include <functional>

#include "graphid.h"
#include "../base/data_interface.h"
#include "costconstants.h"
#include "edge_label.h"
#include "edge_status.h"
#include "graph_reader/graph_reader.h"

namespace aurora {
namespace path {


struct CostingOptions
{
  int32_t strategy;
};

struct Costing
{
    // const CostingOptions &options() const { return options_; }
    CostingOptions options;
};

class DynamicCost {
public:
  DynamicCost(const Costing &options, std::shared_ptr<GraphReader> graph_reader_ptr = nullptr);

  virtual ~DynamicCost();

  DynamicCost(const DynamicCost&) = delete;
  DynamicCost& operator=(const DynamicCost&) = delete;

  virtual float AStarCostFactor() const = 0;

  virtual Cost EdgeCost(const DirectEdgeInfoPtr edge) = 0;
  virtual Cost NodeCost(const EdgeLabel& pred, const NodeInfoPtr& node, const DirectEdgeInfoPtr& edge) = 0;

  virtual bool Allowed(const EdgeLabel& pred, const NodeInfoPtr& node, const DirectEdgeInfoPtr edge) = 0;
  virtual bool Allowed(const NodeInfoPtr node) = 0;

  virtual bool Restricted(const EdgeLabel& pred, const NodeInfoPtr& node, const DirectEdgeInfoPtr edge, EdgeStatus& edge_status, const std::vector<EdgeLabel>& edge_labels,
    bool edge_direction, bool expand_forward);

  virtual bool IsBridgingEdgeRestricted(const std::vector<EdgeLabel>& edge_labels_fwd, const std::vector<EdgeLabel>& edge_labels_rev, const EdgeLabel& fwd_pred, const EdgeLabel& rev_pred);

protected:
   Costing costing_;
   std::shared_ptr<GraphReader> graph_reader_ptr_;
};


} // namespace path
} // namespace aurora

#endif // AURORA_PATH_DYNAMICCOST_H_