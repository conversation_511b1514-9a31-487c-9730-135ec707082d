#ifndef AURORA_PATH_AUTOCOST_H_
#define AURORA_PATH_AUTOCOST_H_

#include "dynamiccost.h"
#include "link_cost.h"

namespace aurora {
namespace path {

class AutoCost : public DynamicCost {
public:
    AutoCost(const Costing& options, std::shared_ptr<GraphReader> graph_reader_ptr = nullptr);
    ~AutoCost() override;

    float AStarCostFactor() const override;

    // Override virtual functions from DynamicCost
    Cost EdgeCost(const DirectEdgeInfoPtr edge) override;
    Cost NodeCost(const EdgeLabel& pred, const NodeInfoPtr& node, const DirectEdgeInfoPtr& edge) override;

    bool Allowed(const EdgeLabel& pred, const NodeInfoPtr& node, const DirectEdgeInfoPtr edge);
    bool Allowed(const NodeInfoPtr node);

private:
    // Add any private members if needed
  std::vector<double> speed_factor_;
  std::vector<double> road_class_factor_;
  float highway_factor_;      // Factor applied when road is a motorway or trunk
  float alley_factor_;        // Avoid alleys factor.
  float toll_factor_;         // Factor applied when road has a toll
  float surface_factor_;      // How much the surface factors are applied.
  float distance_factor_;     // How much distance factors in overall favorability
  float inv_distance_factor_; // How much time factors in overall favorability

  uint32_t top_speed_;

  std::shared_ptr<LinkCost> link_cost_ptr_;
  bool is_time_first_;
};

} // namespace path
} // namespace aurora

#endif // AURORA_PATH_AUTOCOST_H_ 