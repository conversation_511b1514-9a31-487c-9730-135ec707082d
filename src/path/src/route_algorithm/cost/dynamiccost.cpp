#include "dynamiccost.h"

namespace aurora {
namespace path {

DynamicCost::DynamicCost(const Costing& options, std::shared_ptr<GraphReader> graph_reader_ptr)
    : costing_(options), graph_reader_ptr_(graph_reader_ptr) {
}

DynamicCost::~DynamicCost() = default;


bool DynamicCost::Restricted(const EdgeLabel& pred, const NodeInfoPtr& node, const DirectEdgeInfoPtr edge, EdgeStatus& edge_status, const std::vector<EdgeLabel>& edge_labels,
    bool edge_direction, bool expand_forward) {

    auto next_predecessor = [&edge_labels](const EdgeLabel& label) {
        return label.predecessor() == kInvalidLabel ? label : edge_labels[label.predecessor()];
    };

    // reset_edge_stauts
    if (expand_forward) {
        if (edge->has_turn_rule &&edge->is_all_day_limit) {
            // todo: check turn restriction
            auto restrictions = graph_reader_ptr_->GetExitRestrictions(edge->id, edge_direction); // current edge
            if (restrictions.size() == 0) {
                return false;
            }

            for (const auto& cr: restrictions) {
                // 普通禁转
                if (cr->access_ctrl_relation == parser::AccessCtrlRelation::kAccessCtrlRelation0 &&
                    cr->access_ctrl_type == parser::AccessCtrlType::kAccessCtrlType0) {

                    int32_t max_trace_back_num = 6;
                    EdgeLabel current_label = pred;
                    while (max_trace_back_num > 0) {
                        if (current_label.graphid().id() == cr->in_edge_id &&
                            ((current_label.forward() && !cr->in_edge_dir) ||
                            (!current_label.forward() && cr->in_edge_dir))) {
                            return true;
                        }
                        if (current_label.predecessor() == kInvalidLabel) {
                            break;
                        }
                        current_label = next_predecessor(current_label);
                        max_trace_back_num--;
                    }
                }
                // todo: 门禁
            }
        }
    } else {
        // todo
        if (edge->has_turn_rule &&edge->is_all_day_limit) {
            auto restrictions = graph_reader_ptr_->GetEnterRestrictions(edge->id, edge_direction); // current edge
            if (restrictions.size() == 0) {
                return false;
            }

            for (const auto& cr: restrictions) {
                // 普通禁转
                if (cr->access_ctrl_relation == parser::AccessCtrlRelation::kAccessCtrlRelation0 &&
                    cr->access_ctrl_type == parser::AccessCtrlType::kAccessCtrlType0) {
                    int32_t max_trace_back_num = 6;
                    EdgeLabel current_label = pred;
                    while (max_trace_back_num > 0) {
                        if (current_label.graphid().id() == cr->out_edge_id &&
                            ((current_label.forward() && !cr->out_edge_dir) ||
                            (!current_label.forward() && cr->out_edge_dir))) {
                            return true;
                        }
                        if (current_label.predecessor() == kInvalidLabel) {
                            break;
                        }
                        current_label = next_predecessor(current_label);
                        max_trace_back_num--;
                    }
                }
                // todo: 门禁
            }
        }
    }

    return false;
}


} // namespace path
} // namespace aurora 