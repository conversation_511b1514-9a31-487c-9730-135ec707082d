#include "autocost.h"
#include <cmath>

#include "base/graphconstants.h"
#include "base/constants.h"
#include "logger.h"
#include "turn_cost.h"

namespace aurora {
namespace path {

AutoCost::AutoCost(const Costing& options, std::shared_ptr<GraphReader> graph_reader_ptr)
    : DynamicCost(options, graph_reader_ptr) {

  link_cost_ptr_ = std::make_shared<LinkCost>();
  // Create speed cost table
  speed_factor_.resize(kMaxSpeedKph + 1, 0);
  speed_factor_[0] = kSecPerHour; // TODO - what to make speed=0?
  for (uint32_t s = 1; s <= kMaxSpeedKph; s++) {
    speed_factor_[s] = (kSecPerHour * 0.001f) / static_cast<float>(s);
  }

  road_class_factor_.resize(parser::FunctionClass::FC_Count, 1.0f);
  // set all member to 2.0f
  std::fill(road_class_factor_.begin(), road_class_factor_.end(), 2.0f);

  road_class_factor_[parser::FunctionClass::kFunctionClass0] = 1.0f;
  road_class_factor_[parser::FunctionClass::kFunctionClass1] = 1.1f;
  road_class_factor_[parser::FunctionClass::kFunctionClass2] = 1.2f;
  road_class_factor_[parser::FunctionClass::kFunctionClass3] = 1.3f;
  road_class_factor_[parser::FunctionClass::kFunctionClass4] = 1.4f;
  road_class_factor_[parser::FunctionClass::kFunctionClass5] = 1.5f;

  top_speed_ = 140U;
  is_time_first_ = (options.options.strategy == 0);
}

AutoCost::~AutoCost() = default;

float AutoCost::AStarCostFactor() const {
    if (is_time_first_) {
        return speed_factor_[top_speed_] * kSecMultiplier;
    } else {
        return 1.0f;
    }
}

Cost AutoCost::EdgeCost(const DirectEdgeInfoPtr edge) {
    // TODO: Implement edge cost calculation

    // todo: calculate the final speed
    double final_speed = 3.6f; //std::min(static_cast<uint32_t>(edge->speed_), kMaxSpeedKph);
    double sec = edge->length_; // * speed_factor_[final_speed];

    if (is_time_first_) {
        auto cost_sec = link_cost_ptr_->GetLinkCost(edge);
        // auto travel_time = link_cost_ptr_->LinkTravelTime(edge, edge->length_, enTurnType::TURN_STRAIGHT, false);
        // LOG_INFO("travel time: {}, cost_sec: {}, legnth: {}", travel_time, cost_sec, edge->length_);
        return Cost(cost_sec, cost_sec, edge->length_);
    }

    // LOG_INFO("cost_sec: {}", cost_sec);


    // Cost cost1(edge->length_, sec);
    // if (edge->is_area_link) {
    //     cost1 *=100;
    // }
    // return cost;
    return Cost(edge->length_, sec, edge->length_) * road_class_factor_[static_cast<size_t>(edge->function_class)];
}

Cost AutoCost::NodeCost(const EdgeLabel& pred, const NodeInfoPtr& node, const DirectEdgeInfoPtr& edge) {
    // TODO: Implement node cost calculation

    // todo: is_time_first
    double turn_duration = TurnDuration(pred, node, edge);
    turn_duration *= kSecMultiplier;

    // todo: multipy x10

    return Cost(turn_duration, turn_duration, 0.0f);
}

bool AutoCost::Allowed(const EdgeLabel& pred, const NodeInfoPtr& node, const DirectEdgeInfoPtr edge) {
    // TODO: Implement edge allowed calculation
    // if (edge->is_area_link) {
    //     return false;
    // }
    if (!pred.edge_ptr()->is_area_link && edge->is_area_link) {
        return false;
    }

    return true;
}

bool AutoCost::Allowed(const NodeInfoPtr node) {
    // TODO: Implement node allowed calculation
    return true;
}
} // namespace path
} // namespace aurora 