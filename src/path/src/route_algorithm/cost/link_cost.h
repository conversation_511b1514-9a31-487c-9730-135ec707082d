#ifndef LINK_COST_H
#define LINK_COST_H

#include <cstdint>
#include "data_interface.h"
#include "base/constants.h"

namespace aurora {
namespace path {

enum enTurnType {
    TURN_STRAIGHT = 0,
    TURN_LEFT,
    TURN_LEFT_BACK,
    TURN_RIGHT,
    TURN_RIGHT_BACK,
    TURN_BACK,
    TURN_DEST,
    TURN_COUNT
};

class LinkCost {
public:
    // Calculate the travel cost for a road segment
    // Returns time cost in units of 0.1 seconds
    double GetLinkCost(const DirectEdgeInfoPtr edge) {
        return edge->length_ * kSecMultiplier / LinkAverageSpeed(edge);
    }

    // Calculate the average speed for a road segment
    // Returns speed in m/s
    double LinkAverageSpeed(const DirectEdgeInfoPtr edge) {
        // Special road types
        if (edge->edge_type == parser::EdgeType::kEdgeTypeFerry) {
            return 6.9f;  // 25km/h
        }

        if (edge->is_area_link) {
            return 2.77f;  // 5km/h
        }

        if (edge->is_ramp) {
            return 11.11f;  // 40km/h
        }

        if (edge->edge_form == parser::EdgeForm::kEdgeFormJCT) {
            if (edge->road_class == parser::RoadClass::kRoadClassHighway) {
                return 22.22f;  // 80km/h
            } else {
                return 11.11f;  // 40km/h
            }
        }

        // Get lane count index
        uint32_t lane_cnt_index = 0;
        if (edge->direction & 0x01 == 1 ) {
            lane_cnt_index = edge->forward_lane_count;
        } else {
            lane_cnt_index = edge->backward_lane_count;
        }
        lane_cnt_index = (lane_cnt_index > 3 ? 3 : lane_cnt_index);

        // Get base speed from speed table
        double rt_speed = link_speed_[edge->road_class][edge->is_city_edge][lane_cnt_index];

        // Special case adjustments
        if (edge->edge_form == parser::EdgeForm::kEdgeFormIC && rt_speed > 8.33f) {
            rt_speed = 8.33f;  // 30km/h
        }

        // Adjust speed for two-way roads with limited width
        // if (edge->direction == 3) {
        //     switch (lane_cnt_index) {
        //         case 0:
        //             rt_speed = rt_speed * 0.66f;
        //             break;
        //         case 1:
        //             rt_speed = link_speed_[edge->function_class][edge->is_city_edge][0];
        //             break;
        //         case 2:
        //             rt_speed = link_speed_[edge->function_class][edge->is_city_edge][1];
        //             break;
        //         case 3:
        //             rt_speed = link_speed_[edge->function_class][edge->is_city_edge][1];
        //             break;
        //         default:
        //             break;
        //     }
        // }

        return rt_speed;
    }

    // Calculate total travel time for a road segment
    // Returns time in seconds
    double LinkTravelTime(const DirectEdgeInfoPtr edge, const uint32_t& distance, const enTurnType turn_type, bool use_speed_grade = false) {
        double timeTotal = 0.0;

        if (!edge) {
            return timeTotal;
        }

        // Add turn cost
        timeTotal += LINK_TRAVEL_TURN_COST[edge->is_left][turn_type];

        // Add traffic light wait time
        // if (edge->has_traffic_light && !edge->is_inner_link) {
        //     timeTotal += 15.0;  // 15 seconds wait time
        // }

        if (use_speed_grade) {
            timeTotal += (distance * 3.6 / level_speed_grade[edge->speed_grade]);
            return timeTotal;
        }

        // Special road types
        if (edge->road_class == parser::RoadClass::kRoadClassPath
            || edge->edge_form == parser::EdgeForm::kEdgeFormPedestrianStreet
            || IsPathWay(edge)) {
            timeTotal += (distance * 3.6 / 5.0);  // 5km/h
        }
        // Ferry
        else if (edge->edge_type == parser::EdgeType::kEdgeTypeFerry) {
            timeTotal += (distance * 3.6 / 25.0);  // 25km/h
        }
        else {
            // Get lane count
            uint32_t lane_cnt = 0;
            if (edge->direction & 0x01 == 1) {
                lane_cnt = edge->forward_lane_count + 1;
            } else {
                lane_cnt = edge->backward_lane_count + 1;
            }
            lane_cnt = (lane_cnt > 4 ? 4 : lane_cnt);

            // Calculate time based on road class and form
            switch (edge->road_class) {
                case parser::RoadClass::kRoadClassHighway: {
                    if (edge->edge_form == parser::EdgeForm::kEdgeFormNormal
                        || edge->edge_form == parser::EdgeForm::kEdgeFormMainRoad) {
                        timeTotal += (distance / highway_in_city_form_road_travel_speed[lane_cnt]);
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormJCT) {
                        timeTotal += (distance * 3.6 / 60.0);  // 60km/h
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormIC) {
                        timeTotal += (distance * 3.6 / 40.0);  // 40km/h
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormSAPA) {
                        timeTotal += (distance * 3.6 / 20.0);  // 20km/h
                    }
                    else {
                        timeTotal += (distance * 3.6 / 80.0);  // 80km/h
                    }
                    break;
                }
                case parser::RoadClass::kRoadClassNationWay: {
                    if (edge->need_toll&& lane_cnt >= 4) {
                        timeTotal += (distance * 3.6 / 80.0);  // 80km/h
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormNormal
                            || edge->edge_form == parser::EdgeForm::kEdgeFormMainRoad
                            || edge->edge_form == parser::EdgeForm::kEdgeFormSideRoad) {
                        timeTotal += (distance / national_major_form_road_travel_speed[lane_cnt]);
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormSAPA 
                            || edge->edge_form == parser::EdgeForm::kEdgeFormUTurn) {
                        timeTotal += (distance * 3.6 / 20.0);  // 20km/h
                    }
                    else {
                        timeTotal += (distance / national_other_form_road_travel_speed[lane_cnt]);
                    }
                    break;
                }
                // Add other road classes similarly...
                case parser::RoadClass::kRoadClassProvinceWay: {
                    if (edge->need_toll && lane_cnt >= 4) {
                        timeTotal += (distance * 3.6 / 70.0);  // 70km/h
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormNormal
                            || edge->edge_form == parser::EdgeForm::kEdgeFormMainRoad
                            || edge->edge_form == parser::EdgeForm::kEdgeFormSideRoad) {
                        timeTotal += (distance / province_major_form_road_travel_speed[lane_cnt]);
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormSAPA 
                            || edge->edge_form == parser::EdgeForm::kEdgeFormUTurn) {
                        timeTotal += (distance * 3.6 / 20.0);  // 20km/h
                    }
                    else {
                        timeTotal += (distance / province_other_form_road_travel_speed[lane_cnt]);
                    }
                    break;
                }
                case parser::RoadClass::kRoadClassCountyWay: {
                    if (edge->edge_form == parser::EdgeForm::kEdgeFormNormal
                            || edge->edge_form == parser::EdgeForm::kEdgeFormMainRoad
                            || edge->edge_form == parser::EdgeForm::kEdgeFormSideRoad) {
                        timeTotal += (distance / country_major_form_road_travel_speed[lane_cnt]);
                    }
                    else {
                        timeTotal += (distance / country_minor_form_road_travel_speed[lane_cnt]);
                    }
                    break;
                }
                case parser::RoadClass::kRoadClassTownWay: {
                    timeTotal += (distance * 3.6 / 25.0);  // 25km/h
                    break;
                }
                case parser::RoadClass::kRoadClassInCountyRoad: {
                    timeTotal += (distance * 3.6 / 15.0);  // 15km/h
                    break;
                }
                case parser::RoadClass::kRoadClassCityFastWay: {
                    if (edge->need_toll && lane_cnt >= 4) {
                        timeTotal += (distance * 3.6 / 87.0);  // 87km/h
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormNormal
                            || edge->edge_form == parser::EdgeForm::kEdgeFormMainRoad) {
                        timeTotal += (distance * 3.6 / 70.0);  // 70km/h
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormJCT) {
                        timeTotal += (distance * 3.6 / 40.0);  // 40km/h
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormIC) {
                        timeTotal += (distance * 3.6 / 30.0);  // 30km/h
                    }
                    else {
                        timeTotal += (distance * 3.6 / 60.0);  // 60km/h
                    }
                    break;
                }
                case parser::RoadClass::kRoadClassMainRoad: {
                    if (edge->need_toll && lane_cnt >= 4) {
                        timeTotal += (distance * 3.6 / 70.0);  // 70km/h
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormNormal
                            || edge->edge_form == parser::EdgeForm::kEdgeFormMainRoad
                            || edge->edge_form == parser::EdgeForm::kEdgeFormSideRoad) {
                        timeTotal += (distance / main_major_form_road_travel_speed[lane_cnt]);
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormSAPA 
                            || edge->edge_form == parser::EdgeForm::kEdgeFormUTurn) {
                        timeTotal += (distance * 3.6 / 20.0);  // 20km/h
                    }
                    else {
                        timeTotal += (distance / main_other_form_road_travel_speed[lane_cnt]);
                    }
                    break;
                }
                case parser::RoadClass::kRoadClassSecondaryRoad: {
                    if (edge->edge_form == parser::EdgeForm::kEdgeFormNormal
                            || edge->edge_form == parser::EdgeForm::kEdgeFormMainRoad
                            || edge->edge_form == parser::EdgeForm::kEdgeFormSideRoad) {
                        timeTotal += (distance / minor_major_form_road_travel_speed[lane_cnt]);
                    }
                    else if (edge->edge_form == parser::EdgeForm::kEdgeFormSAPA 
                            || edge->edge_form == parser::EdgeForm::kEdgeFormUTurn) {
                        timeTotal += (distance * 3.6 / 20.0);  // 20km/h
                    }
                    else {
                        timeTotal += (distance / minor_other_form_road_travel_speed[lane_cnt]);
                    }
                    break;
                }
                case parser::RoadClass::kRoadClassGeneralRoad: {
                    timeTotal += (distance * 3.6 / 20.0);  // 20km/h
                    break;
                }
                case parser::RoadClass::kRoadClassPath: {
                    timeTotal += (distance * 3.6 / 15.0);  // 15km/h
                    break;
                }
                default: {
                    timeTotal += (distance / 13.89);  // 50km/h
                    break;
                }
            }
        }

        return timeTotal;
    }

private:
    // Speed table for different road types, areas, and lane counts
    // Format: link_speed_[road_rank][is_city_link][lane_count]
    double link_speed_[parser::RoadClass::kRoadClassCount][2][4] = {
        // Highway speeds
        {{22.22f, 27.78f, 30.56f, 30.56f},  // Out city
         {22.22f, 25.00f, 27.78f, 30.56f}}, // In city
        
        // National road speeds
        {{13.89f, 18.06f, 19.44f, 22.22f},  // Out city
         {12.50f, 13.89f, 16.67f, 18.06f}}, // In city
        
        // Provincial road speeds
        {{13.89f, 16.67f, 18.06f, 19.44f},  // Out city
         {12.50f, 13.89f, 15.28f, 16.67f}}, // In city
        
        // County road speeds
        {{11.11f, 12.50f, 13.89f, 15.28f},  // Out city
         {9.72f, 11.11f, 12.50f, 13.89f}},  // In city
        
        // Township road speeds
        {{8.33f, 9.72f, 11.11f, 12.50f},    // Out city
         {8.33f, 9.72f, 11.11f, 12.50f}},   // In city
        
        // Internal road speeds
        {{4.17f, 5.56f, 6.94f, 8.33f},      // Out city
         {4.17f, 5.56f, 6.94f, 8.33f}},     // In city
        
        // City highway speeds
        {{13.89f, 19.44f, 22.22f, 23.61f},  // Out city
         {13.89f, 18.06f, 19.44f, 20.28f}}, // In city
        
        // Main road speeds
        {{11.11f, 13.89f, 16.67f, 18.06f},  // Out city
         {9.72f, 12.50f, 13.89f, 15.28f}},  // In city
        
        // Secondary road speeds
        {{8.33f, 11.11f, 12.50f, 13.89f},   // Out city
         {8.33f, 9.72f, 12.50f, 13.89f}},   // In city
        
        // Normal road speeds
        {{5.56f, 6.94f, 8.33f, 9.72f},      // Out city
         {5.56f, 6.94f, 8.33f, 9.72f}},     // In city
        
        // Narrow path speeds
        {{2.78f, 4.17f, 5.56f, 6.94f},      // Out city
         {2.78f, 4.17f, 5.56f, 6.94f}}      // In city
    };

    // Turn cost table
    const uint32_t LINK_TRAVEL_TURN_COST[2][enTurnType::TURN_COUNT] = {
        {0, 25, 30, 10, 25, 60, 0},  // Right driving , 左舵靠右行驶
        {0, 10, 25, 25, 30, 60, 0}   // Left driving
    };

    // Speed grade table
    const double level_speed_grade[9] = { 120.0, 100.0, 90.0, 70.0, 50.0, 30.0, 20.0, 10.0, 5.0 };

    // Highway speed tables
    const double highway_in_city_form_road_travel_speed[5] = { 16.6, 16.6, 25.0, 28.3, 29.1 };
    const double highway_out_city_form_road_travel_speed[6] = { 16.6, 16.6, 25.0, 28.3, 29.1 };

    // National road speed tables
    const double national_major_form_road_travel_speed[5] = { 12.5, 12.5, 15.6, 16.7, 17.2 };
    const double national_minor_form_road_travel_speed[5] = { 12.5, 12.5, 14.4, 15.0, 15.8 };
    const double national_other_form_road_travel_speed[5] = { 10.0, 10.0, 11.7, 11.9, 12.8 };

    // 省道不同形态道路通行速度
    //40km, 40km, 52km, 56km, 59km
    const float province_major_form_road_travel_speed[5] = {11.1, 11.1, 14.4, 15.5, 16.4};
    const float province_minor_form_road_travel_speed[5] = {10.6, 10.6, 13.1, 13.3, 14.4};

    //30km, 30km,	37km, 40km, 42km
    const float province_other_form_road_travel_speed[5] = {8.3, 8.3, 10.3, 11.1, 11.7};

    // 县道不同形态道路通行速度
    const float country_major_form_road_travel_speed[5] = {9.2, 9.2, 10.6, 11.9, 13.3};
    const float country_minor_form_road_travel_speed[5] = {7.2, 7.2, 8.9, 9.7, 10.6};

    // 主要道路不同形态道路通行速度
    const float main_major_form_road_travel_speed[5] = {9.2, 9.2, 13.1, 13.9, 14.4};
    const float main_minor_form_road_travel_speed[5] = {9.2, 9.2, 11.7, 12.5, 13.3};
    const float main_other_form_road_travel_speed[5] = {6.9, 6.9, 8.9, 9.7, 10.3};

    // 次要道路不同形态道路通行速度
    const float minor_major_form_road_travel_speed[5] = {8.3, 8.3, 10.3, 11.1, 11.7};
    const float minor_minor_form_road_travel_speed[5] = {6.9, 6.9, 8.9, 9.7, 10.6};
    const float minor_other_form_road_travel_speed[5] = {6.4, 6.4, 7.8, 8.3, 8.9};

    // Helper function to check if a road is a pathway
    bool IsPathWay(const DirectEdgeInfoPtr edge) {
        bool is_feel_path_way = false;
        bool is_impassable_way = false;
        bool is_area_link = edge->is_area_link;
        return (is_feel_path_way || is_impassable_way || is_area_link);
    }
};

} // namespace path
} // namespace aurora

#endif // LINK_COST_H 