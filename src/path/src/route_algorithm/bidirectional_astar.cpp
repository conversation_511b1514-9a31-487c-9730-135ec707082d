// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-04-30
//
#include <vector>
#include "path_module.h"
#include "route_algorithm.h"
#include "bidirectional_astar.h"
#include "iostream"

namespace aurora {
namespace path {

constexpr uint32_t kBucketCount = 20000;
// Threshold (seconds) to extend search once the first connection has been found.
// TODO - this is currently set based on some exceptional cases (e.g. routes taking
// the PA Turnpike which have very long edges). Using a metric based on maximum edge
// cost creates large performance drops - so perhaps some other metric can be found?
constexpr float kThresholdDelta = 100; //420.0f;

// Relative cost extension to find alternative routes. It's a multiplier that we apply
// to the optimal route cost in order to get a new cost threshold. This threshold indicates
// an upper bound value cost for alternative routes we're looking for. Due to the fact that
// we can't estimate route cost that goes through some particular edge very precisely, we
// can find alternatives with costs greater than the threshold.
constexpr float kAlternativeCostExtend = 1.2f;

// Maximum number of additional iterations allowed once the first connection has been found.
// For alternative routes we use bigger cost extension than in the case with one route. This
// may lead to a significant increase in the number of iterations (~time). So, we should limit
// iterations in order no to drop performance too much.
constexpr uint32_t kAlternativeIterationsDelta = 100000;

BidirectionalAStar::BidirectionalAStar() {
    // Get configuration values from PathConfig
    const auto& config = PathConfigManager::Instance();

    // Set max reserved labels count from configuration
    max_reserved_labels_count_ = config.Get("algorithms.bidirectional_astar.max_reserved_labels_count", 1000000);

    // Initialize graph reader
    graph_reader_ptr_ = std::make_shared<GraphReader>();

    // Load tile data using center point and match radius from configuration
    PointLL center_pt = config.GetCenterPoint();
    double match_radius = config.GetMatchRadius() / 1000.0; // Convert to appropriate units
    graph_reader_ptr_->LoadTileData(center_pt, 0, match_radius);

    // Initialize other members
    cost_threshold_ = std::numeric_limits<float>::max();
    iterations_threshold_ = std::numeric_limits<uint32_t>::max();
    desired_paths_count_ = 1;
    ignore_hierarchy_limits_ = false;
    cost_diff_ = 0.0f;

    strategy_ = config.Get("strategy", 0);

    LOG_INFO("BidirectionalAStar initialized with max_reserved_labels_count: {}, strategy: {}", max_reserved_labels_count_, strategy_);
}

BidirectionalAStar::~BidirectionalAStar() = default;

std::vector<PathInfo> BidirectionalAStar::CalcRoute(const PathQuery& query) {
    LOG_INFO("BidirectionalAStar::CalcRoute");
    ScopeTime scope_time("CalcRoute");

    // Store the query for later use
    query_ = query;

    auto start_landmark = query.path_points.front();
    auto end_landmark = query.path_points.back();

    // Initialize the algorithm
    Init(start_landmark->pt, end_landmark->pt);

    // Set origin and destination, Check if origin or destination are invalid
    if (!SetOrign(start_landmark)) {
        LOG_INFO("Origin has no candidates");
        return {};
    }

    if (!SetDestination(end_landmark)) {
        LOG_INFO("destination has no candidates");
        return {};
    }

    // Main bidirectional A* loop
    int n = 0;
    uint32_t forward_pred_idx{0}, reverse_pred_idx{0};
    EdgeLabel fwd_pred;
    EdgeLabel rev_pred;
    bool expand_forward = true;
    bool expand_reverse = true;

    while (true) {
        // Check iteration threshold
        if ((edgelabels_reverse_.size() + edgelabels_forward_.size()) > iterations_threshold_) {
            LOG_INFO("Exceeded iteration threshold: {}", iterations_threshold_);
            if (!best_connection_.empty()) {
                return FormPath();
            }
            return {};
        }

        // Forward search expansion
        if (expand_forward) {
            forward_pred_idx = adjacencylist_forward_.pop();

            // Check if forward queue is empty
            if (forward_pred_idx == kInvalidLabel) {
                LOG_INFO("Forward search exhausted");
                if (!best_connection_.empty()) {
                    return FormPath();
                }
                return {};
            } else {
                // Get the edge label and update status
                fwd_pred = edgelabels_forward_[forward_pred_idx];
                forward_edge_status_.Update(fwd_pred.graphid(), fwd_pred.forward(), EdgeSet::kPermanent);

                // Check if cost exceeds threshold
                if (fwd_pred.cost().cost > cost_threshold_) {
                    LOG_INFO("Forward cost exceeds threshold: {} > {}",
                             fwd_pred.cost().cost, cost_threshold_);
                    if (!best_connection_.empty()) {
                        return FormPath();
                    }
                }

                // Check for connection to reverse search
                SetForwardConnection(fwd_pred);
            }
        }

        // Reverse search expansion
        if (expand_reverse) {
            reverse_pred_idx = adjacencylist_reverse_.pop();

            // Check if reverse queue is empty
            if (reverse_pred_idx == kInvalidLabel) {
                LOG_INFO("Reverse search exhausted");
                if (!best_connection_.empty()) {
                    return FormPath();
                }
                return {};
            } else {
                // Get the edge label and update status
                rev_pred = edgelabels_reverse_[reverse_pred_idx];
                backward_edge_status_.Update(rev_pred.graphid(), rev_pred.forward(), EdgeSet::kPermanent);

                // Check if cost exceeds threshold
                if (rev_pred.cost().cost > cost_threshold_) {
                    LOG_INFO("Reverse cost exceeds threshold: {} > {}",
                             rev_pred.cost().cost, cost_threshold_);
                    if (!best_connection_.empty()) {
                        return FormPath();
                    }
                }

                // Check for connection to forward search
                SetReverseConnection(rev_pred);
            }
        }

        // Determine which direction to expand next based on sort costs
        if (fwd_pred.sortcost() < rev_pred.sortcost()) {
            expand_forward = true;
            expand_reverse = false;

            // Expand from the forward edge
            Expand<true>(fwd_pred.graphid(), fwd_pred, forward_pred_idx);
        } else {
            expand_forward = false;
            expand_reverse = true;

            // Expand from the reverse edge
            Expand<false>(rev_pred.graphid(), rev_pred, reverse_pred_idx);
        }

        // Periodically log progress
        if (++n % 10000 == 0) {
            LOG_INFO("Processed {} iterations, forward: {}, reverse: {}",
                     n, edgelabels_forward_.size(), edgelabels_reverse_.size());
        }

        // Check if we've found a connection
        if (!best_connection_.empty() &&
            (fwd_pred.cost().cost + rev_pred.cost().cost) > (cost_threshold_ * 1.1)) {
            LOG_INFO("Found connection and costs exceed threshold by 10%");
            return FormPath();
        }
    }

    return {};
}

void BidirectionalAStar::Clear() {
    edgelabels_forward_.clear();
    edgelabels_reverse_.clear();

    adjacencylist_forward_.clear();
    adjacencylist_reverse_.clear();

    forward_edge_status_.clear();
    backward_edge_status_.clear();

}

void BidirectionalAStar::Init(const PointLL& orig, const PointLL& dest) {
    ScopeTime scope_time("Init");

    // Create costing object
    Costing costing_options;
    costing_options.options.strategy = strategy_;
    costing_ = std::make_shared<AutoCost>(costing_options, graph_reader_ptr_);

    // Initialize A* heuristics
    heuristic_forward_.Init(dest, costing_->AStarCostFactor());
    heuristic_backward_.Init(orig, costing_->AStarCostFactor());

    // Reserve space for edge labels
    edgelabels_forward_.reserve(max_reserved_labels_count_);
    edgelabels_reverse_.reserve(max_reserved_labels_count_);

    // Initialize adjacency lists
    const float mincostf = heuristic_forward_.Get(orig);
    const float range = 1000000.0f;
    const uint32_t bucketsize = 1;
    adjacencylist_forward_.reuse(mincostf, range, bucketsize, &edgelabels_forward_);

    const float mincostr = heuristic_backward_.Get(dest);
    adjacencylist_reverse_.reuse(mincostr, range, bucketsize, &edgelabels_reverse_);

    // Calculate cost difference for bidirectional search balancing
    cost_diff_ = mincostf - mincostr;
    LOG_INFO("Cost difference: {:03.2f}", cost_diff_);

    // Initialize connection tracking
    best_connection_.clear();
    cost_threshold_ = std::numeric_limits<float>::max();
    iterations_threshold_ = std::numeric_limits<uint32_t>::max();

    // Initialize hierarchy limits for multi-level routing
    hierarchy_limits_forward_.clear();
    hierarchy_limits_reverse_.clear();

    // Set up hierarchy limits for each level (0, 1, 2)
    for (uint32_t level = 0; level < 3; level++) {
        HierarchyLimits forward_limits;
        HierarchyLimits reverse_limits;

        // Level 0 (local roads) - no limits on transitions
        if (level == 0) {
            forward_limits.set_max_up_transitions(200);
            forward_limits.set_expand_within_dist(5000.0f);
            reverse_limits.set_max_up_transitions(200);
            reverse_limits.set_expand_within_dist(5000.0f);
        }
        // Level 1 (arterial roads) - moderate limits
        else if (level == 1) {
            forward_limits.set_max_up_transitions(100);
            forward_limits.set_expand_within_dist(10000.0f);
            reverse_limits.set_max_up_transitions(100);
            reverse_limits.set_expand_within_dist(10000.0f);
        }
        // Level 2 (highways) - stricter limits
        else if (level == 2) {
            // No limits on transitions
            forward_limits.set_max_up_transitions(std::numeric_limits<uint32_t>::max());
            forward_limits.set_expand_within_dist(std::numeric_limits<float>::max());
            reverse_limits.set_max_up_transitions(std::numeric_limits<uint32_t>::max());
            reverse_limits.set_expand_within_dist(std::numeric_limits<float>::max());
        }

        hierarchy_limits_forward_.push_back(forward_limits);
        hierarchy_limits_reverse_.push_back(reverse_limits);
    }

    // Set flag to ignore hierarchy limits if needed
    ignore_hierarchy_limits_ = false;

    LOG_INFO("Bidirectional A* initialized with {} hierarchy levels", hierarchy_limits_forward_.size());
}

bool BidirectionalAStar::SetOrign(const PathLandmarkPtr& origin) {
    ScopeTime scope_time("SetOrigin");
    if (!origin || !origin->valid) {
        LOG_INFO("Origin is invalid");
        return false;
    }

    graph_reader_ptr_->StaticMatching(origin, PathConfigManager::Instance().GetMatchRadius());

    if (origin->candidates.empty()) {
        LOG_INFO("Origin has no candidates");
        return false;
    }
    origin_ = origin;

    // Add each origin candidate to the adjacency list
    for (const auto& candidate : origin->candidates) {
        // Calculate the cost from the origin to the edge
        GraphId edge_id = {candidate.link->tile_id, candidate.link->id};
        auto edgePtr = graph_reader_ptr_->GetDirectEdgeInfo(edge_id);
        if (!edgePtr) {
            LOG_INFO("Failed to get edge info for origin edge: {}", edge_id.ToString());
            continue;
        }

        Cost edge_cost = costing_->EdgeCost(edgePtr);
        Cost node_cost{};

        // Forward direction
        if (edgePtr->is_forward()) {
            Cost cost = edge_cost * (1.0f - (candidate.offset / candidate.link->length));

            // In A*, the sort cost is the cost + heuristic
            float sortcost = cost.cost;
            auto node_ptr = graph_reader_ptr_->GetNodeInfo(GraphId(edgePtr->id.tileid(), edgePtr->endnode_));
            if (node_ptr) {
                sortcost += heuristic_forward_.Get(node_ptr->point);
            }

            // Add to the edge labels
            uint32_t idx = edgelabels_forward_.size();
            forward_edge_status_.Set(edge_id, true, EdgeSet::kTemporary, idx);
            edgelabels_forward_.emplace_back(kInvalidLabel, edge_id, edge_id.id(), edge_id.tileid(), cost, sortcost, node_cost, edgePtr, true);

            // Add to the adjacency list
            adjacencylist_forward_.add(idx);
        }

        // Reverse direction
        if (edgePtr->is_reverse()) {
            Cost cost = edge_cost * (candidate.offset / candidate.link->length);

            // In A*, the sort cost is the cost + heuristic
            float sortcost = cost.cost;
            auto node_ptr = graph_reader_ptr_->GetNodeInfo(GraphId(edgePtr->id.tileid(), edgePtr->startnode_));
            if (node_ptr) {
                sortcost += heuristic_forward_.Get(node_ptr->point);
            }

            // Add to the edge labels
            uint32_t idx = edgelabels_forward_.size();
            forward_edge_status_.Set(edge_id, false, EdgeSet::kTemporary, idx);
            edgelabels_forward_.emplace_back(kInvalidLabel, edge_id, edge_id.id(), edge_id.tileid(), cost, sortcost, node_cost, edgePtr, false);

            // Add to the adjacency list
            adjacencylist_forward_.add(idx);
        }
    }
    return true;
}

bool BidirectionalAStar::SetDestination(const PathLandmarkPtr& destination) {
    ScopeTime scope_time("SetDestination");
    if (!destination || !destination->valid) {
        LOG_INFO("Destination is invalid");
        return false;
    }

    if (!graph_reader_ptr_->StaticMatching(destination, PathConfigManager::Instance().GetMatchRadius())) {
        LOG_INFO("Failed to match destination");
        return false;
    }

    if (destination->candidates.empty()) {
        LOG_INFO("Destination has no candidates");
        return false;
    }
    destination_ = destination;

    // Add each destination candidate to the adjacency list
    for (const auto& candidate : destination->candidates) {
        // Calculate the cost from the destination to the edge
        GraphId edge_id = {candidate.link->tile_id, candidate.link->id};
        auto edgePtr = graph_reader_ptr_->GetDirectEdgeInfo(edge_id);
        if (!edgePtr) {
            LOG_INFO("Failed to get edge info for destination edge: {}", edge_id.ToString());
            continue;
        }

        Cost edge_cost = costing_->EdgeCost(edgePtr);
        Cost node_cost{};

        // Forward direction (for reverse search)
        if (edgePtr->is_forward()) {
            Cost cost = edge_cost * (candidate.offset / candidate.link->length);

            // In A*, the sort cost is the cost + heuristic
            float sortcost = cost.cost;
            auto node_ptr = graph_reader_ptr_->GetNodeInfo(GraphId(edgePtr->id.tileid(), edgePtr->startnode_));
            if (node_ptr) {
                sortcost += heuristic_backward_.Get(node_ptr->point);
            }

            // Add to the edge labels
            uint32_t idx = edgelabels_reverse_.size();
            backward_edge_status_.Set(edge_id, true, EdgeSet::kTemporary, idx);
            edgelabels_reverse_.emplace_back(kInvalidLabel, edge_id, edge_id.id(), edge_id.tileid(), cost, sortcost, node_cost,edgePtr, true);

            // Add to the adjacency list
            adjacencylist_reverse_.add(idx);
        }

        // Reverse direction (for reverse search)
        if (edgePtr->is_reverse()) {
            Cost cost = edge_cost * (1.0f - (candidate.offset / candidate.link->length));

            // In A*, the sort cost is the cost + heuristic
            float sortcost = cost.cost;
            auto node_ptr = graph_reader_ptr_->GetNodeInfo(GraphId(edgePtr->id.tileid(), edgePtr->endnode_));
            if (node_ptr) {
                sortcost += heuristic_backward_.Get(node_ptr->point);
            }

            // Add to the edge labels
            uint32_t idx = edgelabels_reverse_.size();
            backward_edge_status_.Set(edge_id, false, EdgeSet::kTemporary, idx);
            edgelabels_reverse_.emplace_back(kInvalidLabel, edge_id, edge_id.id(), edge_id.tileid(), cost, sortcost, node_cost, edgePtr, false);

            // Add to the adjacency list
            adjacencylist_reverse_.add(idx);
        }
    }
    return true;
}

template <bool forward>
void BidirectionalAStar::Expand(GraphId id, EdgeLabel pred, uint32_t pred_idx) {
    // Get the node info
    auto expand_node_id = forward ? (pred.forward() ? pred.edge_ptr()->endnode_ : pred.edge_ptr()->startnode_)
                                  : (pred.forward() ? pred.edge_ptr()->startnode_ : pred.edge_ptr()->endnode_);
    auto unique_node_id = GraphId(id.tileid(), expand_node_id);
    auto node_ptr = graph_reader_ptr_->GetNodeInfo(unique_node_id);

    if (!node_ptr) {
        LOG_INFO("Node not found: {}", unique_node_id.ToString());
        return;
    }

    // Check if costing allows access to this node
    if (!costing_->Allowed(node_ptr)) {
        return;
    }

    // Get hierarchy limits
    auto& hierarchy_limits = forward ? hierarchy_limits_forward_ : hierarchy_limits_reverse_;

    // Check for upward transitions
    if (node_ptr->is_tranup) {
      // Increment the transition count
      hierarchy_limits[node_ptr->level].set_up_transition_count(
          hierarchy_limits[node_ptr->level].up_transition_count() + 1);

      // Get the node in the higher level
      auto up_node_ptr = graph_reader_ptr_->GetNodeInfo(node_ptr->tranup_id);
      if (up_node_ptr) {
        // LOG_INFO("Expanding upward to level {}", up_node_ptr->level);
        // Expand at the higher level
        ExpandInner<forward>(node_ptr->tranup_id, pred, pred_idx);
      }
    }

    // Check for downward transitions
    // if (node_ptr->is_trandown) {
    //     // Handle downward transition (always allowed)
    //     auto down_node_ptr = graph_reader_ptr_->GetNodeInfo(node_ptr->transdown_id);
    //     if (down_node_ptr) {
    //         LOG_INFO("Expanding downward to level {}", down_node_ptr->level);
    //         // Expand at the lower level
    //         ExpandInner<forward>(node_ptr->transdown_id, pred, pred_idx);
    //     }
    // }


    // Handle stopping at the current level transition if allowed by hierarchy limits
    const auto& pt = node_ptr->point;
    // double search_distance = 0.0f;
    // if (forward) {
    //     search_distance = heuristic_backward_.GetDistance(node_ptr->point);
    // } else {
    //     search_distance = heuristic_forward_.GetDistance(node_ptr->point);
    // }
    if (!ignore_hierarchy_limits_ &&
        StopExpanding(hierarchy_limits[node_ptr->level], pred.cost().length)) { //pred.cost().cost， pred.cost().length
    //   LOG_INFO("Skip current level expand due to hierarchy limits");
    } else {
      // Expand at the current level
      ExpandInner<forward>(unique_node_id, pred, pred_idx);
    }
}

template <bool forward>
void BidirectionalAStar::ExpandInner(GraphId id, EdgeLabel pred, uint32_t pred_idx) {
    // Get the node info
    auto node_ptr = graph_reader_ptr_->GetNodeInfo(id);
    if (!node_ptr) {
        LOG_INFO("Node not found in ExpandInner: {}", id.ToString());
        return;
    }

    // Get outgoing edges
    std::vector<DirectEdgeInfoPtr> out_edges;
    if (forward) {
        graph_reader_ptr_->GetExitEdges(node_ptr, out_edges);
    } else {
        graph_reader_ptr_->GetEnterEdges(node_ptr, out_edges);
    }

    // Also check boundary nodes for connected edges
    std::vector<DirectEdgeInfoPtr> adj_out_edges;
    if (!node_ptr->boundary_node_ids.empty()) {
        for (const auto& adj_node_id : node_ptr->boundary_node_ids) {
            auto adj_node_ptr = graph_reader_ptr_->GetNodeInfo(adj_node_id);
            if (adj_node_ptr) {
                forward ? graph_reader_ptr_->GetExitEdges(adj_node_ptr, adj_out_edges)
                        : graph_reader_ptr_->GetEnterEdges(adj_node_ptr, adj_out_edges);
            }
        }
    }

    // Combine all outgoing edges
    out_edges.insert(out_edges.end(), adj_out_edges.begin(), adj_out_edges.end());

    // Helper function to check if a node matches the expand node
    const auto is_node_match = [&node_ptr, &id](GraphId node_id) {
        if (node_id == id) {
            return true;
        }
        return std::find(node_ptr->boundary_node_ids.begin(), node_ptr->boundary_node_ids.end(), node_id)
            != node_ptr->boundary_node_ids.end();
    };

    // Expand to all outgoing edges
    for (const auto& outedge : out_edges) {
        // Skip the edge we came from (avoid U-turns)
        if (outedge->id == pred.graphid()) {
            continue;
        }

        // Check if costing allows this edge
        if (!costing_->Allowed(pred, node_ptr, outedge)) {
            continue;
        }

        // Forward direction
        bool node_matched = forward ? is_node_match(GraphId(outedge->id.tileid(), outedge->startnode_))
                                  : is_node_match(GraphId(outedge->id.tileid(), outedge->endnode_));
        if (outedge->is_forward() && node_matched) {
            // Skip if this edge is already permanently labeled
            auto& edge_status = forward ? forward_edge_status_ : backward_edge_status_;
            if (edge_status.Get(outedge->id, true).set() == EdgeSet::kPermanent) {
                continue;
            }

            if (outedge->id.tileid() == 25589389380074224 && outedge->id.id() == 30) {
                LOG_INFO("Tile id is 25589389380074224, id {}", outedge->id.ToString());
            }

            if (costing_->Restricted(pred, node_ptr, outedge, edge_status, forward ? edgelabels_forward_ : edgelabels_reverse_, true, forward)) {
                LOG_INFO("Restricted, id {}", outedge->id.ToString());
                continue;
            }

            // Calculate cost
            Cost edgecost = costing_->EdgeCost(outedge);
            Cost nodecost = costing_->NodeCost(pred, node_ptr, outedge);
            // todo： add node cost
            Cost newcost = pred.cost() + edgecost + nodecost;

            // Calculate sort cost with heuristic
            float sortcost = newcost.cost;
            auto end_node_ptr = graph_reader_ptr_->GetNodeInfo(GraphId(outedge->id.tileid(), forward ? outedge->endnode_ : outedge->startnode_));
            if (end_node_ptr) {
                sortcost += forward ?
                    heuristic_forward_.Get(end_node_ptr->point) :
                    heuristic_backward_.Get(end_node_ptr->point);
            }

            // Check if this is a new edge or a better path to an existing edge
            EdgeStatusInfo status = edge_status.Get(outedge->id, true);
            if (status.set() == EdgeSet::kTemporary) {
                uint32_t idx = status.index();
                auto& edgelabels = forward ? edgelabels_forward_ : edgelabels_reverse_;
                auto& adjacencylist = forward ? adjacencylist_forward_ : adjacencylist_reverse_;

                if (newcost.cost < edgelabels[idx].cost().cost) {
                    // Update with the new cost
                    float newsortcost = edgelabels[idx].sortcost() - (edgelabels[idx].cost().cost - newcost.cost);
                    // float newsortcost = sortcost;
                    adjacencylist.decrease(idx, newsortcost);
                    edgelabels[idx] = EdgeLabel(pred_idx, outedge->id, outedge->id.id(),
                                               outedge->id.tileid(), newcost, newsortcost, nodecost, outedge, true);
                }
            } else {
                // Add a new edge label
                uint32_t idx = forward ? edgelabels_forward_.size() : edgelabels_reverse_.size();
                auto& edgelabels = forward ? edgelabels_forward_ : edgelabels_reverse_;
                auto& adjacencylist = forward ? adjacencylist_forward_ : adjacencylist_reverse_;

                edgelabels.emplace_back(pred_idx, outedge->id, outedge->id.id(),
                                       outedge->id.tileid(), newcost, sortcost, nodecost, outedge, true);
                adjacencylist.add(idx);
                edge_status.Set(outedge->id, true, EdgeSet::kTemporary, idx);

                // Check if this edge creates a connection between forward and reverse searches
                if (forward) {
                    SetForwardConnection(edgelabels.back());
                } else {
                    SetReverseConnection(edgelabels.back());
                }
            }
        }

        // Reverse direction
        node_matched = forward ? is_node_match(GraphId(outedge->id.tileid(), outedge->endnode_))
                                  : is_node_match(GraphId(outedge->id.tileid(), outedge->startnode_));
        if (outedge->is_reverse() && node_matched) {
            // Skip if this edge is already permanently labeled
            auto& edge_status = forward ? forward_edge_status_ : backward_edge_status_;
            if (edge_status.Get(outedge->id, false).set() == EdgeSet::kPermanent) {
                continue;
            }

            if (costing_->Restricted(pred, node_ptr, outedge, edge_status, forward ? edgelabels_forward_ : edgelabels_reverse_, false, forward)) {
                LOG_INFO("Restricted, id {}", outedge->id.ToString());
                continue;
            }

            // Calculate cost
            Cost edgecost = costing_->EdgeCost(outedge);
            Cost nodecost = costing_->NodeCost(pred, node_ptr, outedge);
            Cost newcost = pred.cost() + edgecost + nodecost;

            // Calculate sort cost with heuristic
            float sortcost = newcost.cost;
            auto start_node_ptr = graph_reader_ptr_->GetNodeInfo(GraphId(outedge->id.tileid(), forward ? outedge->startnode_ : outedge->endnode_));
            if (start_node_ptr) {
                sortcost += forward ?
                    heuristic_forward_.Get(start_node_ptr->point) :
                    heuristic_backward_.Get(start_node_ptr->point);
            }

            // Check if this is a new edge or a better path to an existing edge
            EdgeStatusInfo status = edge_status.Get(outedge->id, false);
            if (status.set() == EdgeSet::kTemporary) {
                uint32_t idx = status.index();
                auto& edgelabels = forward ? edgelabels_forward_ : edgelabels_reverse_;
                auto& adjacencylist = forward ? adjacencylist_forward_ : adjacencylist_reverse_;

                if (newcost.cost < edgelabels[idx].cost().cost) {
                    // Update with the new cost
                    float newsortcost = edgelabels[idx].sortcost() - (edgelabels[idx].cost().cost - newcost.cost);
                    // float newsortcost = sortcost;
                    adjacencylist.decrease(idx, newsortcost);
                    edgelabels[idx] = EdgeLabel(pred_idx, outedge->id, outedge->id.id(),
                                               outedge->id.tileid(), newcost, newsortcost, nodecost, outedge, false);
                }
            } else {
                // Add a new edge label
                uint32_t idx = forward ? edgelabels_forward_.size() : edgelabels_reverse_.size();
                auto& edgelabels = forward ? edgelabels_forward_ : edgelabels_reverse_;
                auto& adjacencylist = forward ? adjacencylist_forward_ : adjacencylist_reverse_;

                edgelabels.emplace_back(pred_idx, outedge->id, outedge->id.id(),
                                       outedge->id.tileid(), newcost, sortcost, nodecost, outedge, false);
                adjacencylist.add(idx);
                edge_status.Set(outedge->id, false, EdgeSet::kTemporary, idx);

                // Check if this edge creates a connection between forward and reverse searches
                if (forward) {
                    SetForwardConnection(edgelabels.back());
                } else {
                    SetReverseConnection(edgelabels.back());
                }
            }
        }
    }
}


bool BidirectionalAStar::SetForwardConnection(const EdgeLabel& pred) {
    // Check if this edge is already in the reverse edge labels
    auto back_edge_status = backward_edge_status_.Get(pred.graphid(), pred.forward());
    if (back_edge_status.set() == EdgeSet::kUnreachedOrReset) {
        return false;
    }
    auto rev_pred = edgelabels_reverse_[back_edge_status.index()];

    if (IsOrignDestLoop(pred, rev_pred)) {
        LOG_INFO("origin loop to dest");
        return false;
    }
    // todo: fix cost in one edge
    // Found a connection, create a candidate connection
    CandidateConnection connection;
    connection.edgeid = pred.graphid();
    connection.opp_edgeid = rev_pred.graphid();
    connection.forward = pred.forward();

    double c;
    if (pred.predecessor() != kInvalidLabel) {
        c = edgelabels_forward_[pred.predecessor()].cost().cost + rev_pred.cost().cost + pred.transition_cost().cost;
    } else {
        c = pred.cost().cost;
        uint32_t predidx = rev_pred.predecessor();
        if (predidx != kInvalidLabel) {
            c += edgelabels_reverse_[predidx].cost().cost + rev_pred.transition_cost().cost;
        } else {
            c += rev_pred.transition_cost().cost;
        }
    }
    connection.cost = c;

    connection.forward_index =
        forward_edge_status_.Get(pred.graphid(), pred.forward()).index();
    connection.reverse_index =
        backward_edge_status_.Get(rev_pred.graphid(), rev_pred.forward())
            .index();
    connection.UpdatePathEdges(edgelabels_forward_, edgelabels_reverse_);

    // Add to best connections if it's better than the current threshold
    if (connection.cost < cost_threshold_) {
      best_connection_.push_back(connection);
      // Sort connections by cost
      std::sort(best_connection_.begin(), best_connection_.end());
      // Update the cost threshold
    //   cost_threshold_ = best_connection_[0].cost + 100.0;  // Add a buffer
      cost_threshold_ = kAlternativeCostExtend * best_connection_[0].cost + kThresholdDelta;

      iterations_threshold_ = edgelabels_forward_.size() + edgelabels_reverse_.size() + kAlternativeIterationsDelta;
      LOG_INFO_EVERY_N(100, "Found forward connection with cost: {}", connection.cost);
      return true;
    }
    return false;
}

bool BidirectionalAStar::SetReverseConnection(const EdgeLabel& pred) {
    auto forward_edge_status = forward_edge_status_.Get(pred.graphid(), pred.forward());
    // Check if this edge is already in the forward edge labels
    if (forward_edge_status.set() == EdgeSet::kUnreachedOrReset) {
        return false;
    }
    auto fwd_pred = edgelabels_forward_[forward_edge_status.index()];
    if (IsOrignDestLoop(fwd_pred, pred)) {
        LOG_INFO("origin loop to dest");
        return false;
    }
    // Found a connection, create a candidate connection
    CandidateConnection connection;
    connection.edgeid = fwd_pred.graphid();
    connection.opp_edgeid = pred.graphid();
    connection.forward = fwd_pred.forward();

    double c;
    if (pred.predecessor() != kInvalidLabel) {
        c = edgelabels_reverse_[pred.predecessor()].cost().cost + fwd_pred.cost().cost + pred.transition_cost().cost;
    } else {
        c = pred.cost().cost;
        uint32_t predidx = fwd_pred.predecessor();
        if (predidx != kInvalidLabel) {
            c += edgelabels_forward_[predidx].cost().cost + fwd_pred.transition_cost().cost;
        } else {
            c += fwd_pred.transition_cost().cost;
        }
    }
    connection.cost = c;

    connection.reverse_index =
        backward_edge_status_.Get(pred.graphid(), pred.forward()).index();
    connection.forward_index =
        forward_edge_status_.Get(fwd_pred.graphid(), fwd_pred.forward())
            .index();
    connection.UpdatePathEdges(edgelabels_forward_, edgelabels_reverse_);

    // Add to best connections if it's better than the current threshold
    if (connection.cost < cost_threshold_) {
      best_connection_.push_back(connection);
      // Sort connections by cost
      std::sort(best_connection_.begin(), best_connection_.end());
      // Update the cost threshold
      // cost_threshold_ = best_connection_[0].cost + 100.0;  // Add a buffer
      cost_threshold_ = kAlternativeCostExtend * best_connection_[0].cost + kThresholdDelta;
      iterations_threshold_ = edgelabels_forward_.size() + edgelabels_reverse_.size() + kAlternativeIterationsDelta;
      LOG_INFO_EVERY_N(100, "Found reverse connection with cost: {}", connection.cost);
      return true;
    }
    return false;
}

bool BidirectionalAStar::IsOrignDestLoop(const EdgeLabel& fwd_pred, const EdgeLabel& rev_pred) {
  if (fwd_pred.predecessor() == kInvalidLabel &&
      rev_pred.predecessor() == kInvalidLabel) {
    // Find the corresponding candidates from origin and destination

    // Get candidate by graphid
    auto find_candidate = [this](const GraphId& graphid,
                                 bool forward) -> const Candidate* {
      auto land_ptr = forward ? origin_ : destination_;
      for (const auto& candidate : land_ptr->candidates) {
        if (candidate.link && candidate.link->tile_id == graphid.tileid() &&
            candidate.link->id == graphid.local_id) {
          return &candidate;
        }
      }
      return nullptr;
    };
    auto fwd_candidate = find_candidate(fwd_pred.graphid(), true);
    auto rev_candidate = find_candidate(rev_pred.graphid(), false);

    // Check if both candidates exist
    if (!fwd_candidate || !rev_candidate) {
      return false;
    }

    // Check the offset conditions based on forward direction
    if (fwd_pred.forward()) {
      if (fwd_candidate->offset > rev_candidate->offset) {
        return true;
      }
    } else {
      if (fwd_candidate->offset < rev_candidate->offset) {
        return true;
      }
    }
  }
  return false;
}

std::vector<PathInfo> BidirectionalAStar::FormPath() {
    // Check if we have any connections
    if (best_connection_.empty()) {
        LOG_INFO("BidirectionalAStar: No path found - no connections");
        return {};
    }

    // For debugging
    WriteVisitedEdges("bidirectional_forward", edgelabels_forward_);
    WriteVisitedEdges("bidirectional_reverse", edgelabels_reverse_);

    // Use the path ranking system to find alternative paths
    PathRanking path_ranking(graph_reader_ptr_);
    auto paths = path_ranking.FindAlternativePaths(
        edgelabels_forward_,
        edgelabels_reverse_,
        best_connection_,
        origin_,
        destination_
    );

    // Debug output for alternative paths
    if (PathConfigManager::Instance().IsDebugEnabled() && !paths.empty()) {
        for (size_t i = 0; i < paths.size(); ++i) {
            std::string route_file = PathConfigManager::Instance().GetDebugOutPath() +
                                    "/route_alternative_" + std::to_string(i) + ".geojson";
            std::vector<DirectEdgeInfoPtr> direct_edges;
            for (const auto& link : paths[i].links) {
                auto edge_ptr = graph_reader_ptr_->GetDirectEdgeInfo(GraphId(link->tile_id, link->id));
                if (edge_ptr) {
                    direct_edges.push_back(edge_ptr);
                }
            }
            geojson_writter_.WriteDirectEdgeInfos(direct_edges, route_file);
            LOG_INFO("Alternative path {}: length={:.2f}m, time={:.2f}s, edges={}",
                    i, paths[i].length, paths[i].travel_time, paths[i].links.size());
        }
    }

    return paths;
}

void BidirectionalAStar::WriteVisitedEdges(const std::string& filename, const std::vector<EdgeLabel>& edge_labels) {
    std::vector<DirectEdgeInfoPtr> edges;
    for (const auto& label : edge_labels) {
        if (label.edge_ptr()) {
            edges.push_back(label.edge_ptr());
        }
    }

    if (PathConfigManager::Instance().IsDebugEnabled()) {
        std::string output_file = PathConfigManager::Instance().GetDebugOutPath() + "/debug_" + filename + ".geojson";
        geojson_writter_.WriteDirectEdgeInfos(edges, output_file);
        LOG_INFO("Wrote {} edges to {}", edges.size(), output_file);
        std::string out_file = PathConfigManager::Instance().GetDebugOutPath() + "/astar_debug_" + filename +  "_labels.geojson";

        // Write edge labels to file
        geojson_writter_.WriteEdgeLabels(edge_labels, out_file);
    }
}

}  // namespace path
}  // namespace aurora
