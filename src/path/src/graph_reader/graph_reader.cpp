// Copyright (c) 2025 BYD Corporation. All rights reserved.
// Author: <EMAIL>
//
// Created: 2025-05-12

#include "graph_reader.h"

namespace aurora {
namespace path {

GraphReader::GraphReader()
    : tile_(nullptr),
      tile_cache_(std::make_shared<TileCache>()),
      data_provider_ptr_(std::make_shared<aurora::parser::DataProvider>())
{
    tile_cache_->Reserve(1000);
    InitDataProvider();
}

GraphReader::GraphReader(const std::string& data_dir)
    : tile_(nullptr),
      tile_cache_(std::make_shared<TileCache>()),
      data_provider_ptr_(std::make_shared<aurora::parser::DataProvider>()),
      data_dir_(data_dir) {
  tile_cache_->Reserve(1000);
  InitDataProvider();
}

GraphReader::~GraphReader() {}

bool GraphReader::InitDataProvider() {

    // Get data directory from configuration
    std::string data_dir = data_dir_; // Default value
    try {
        const auto& config = aurora::path::PathConfigManager::Instance();
        if (config.HasKey("data_dir")) {
            data_dir = config.GetDataDir();
            LOG_INFO("Using data directory from config: {}", data_dir);
        } else {
            LOG_WARN("No data_dir in config, using default: {}", data_dir);
        }
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to get data directory from config: {}", e.what());
    }

    std::filesystem::path data_path(data_dir);
    if (!std::filesystem::exists(data_path)) {
        LOG_FATAL("data_dir not found");
        return false;
    }

    // Initialize route parser with data directory from configuration
    bool ret = data_provider_ptr_->InitRouteParser(data_dir.c_str());
    LOG_INFO("Initialized route parser with data directory: {}, ret: {}", data_dir, ret);
    return true;
}

bool GraphReader::LoadGeoJsonMap(const std::string& edge_file, const std::string& node_file) {
    // Use GeoJsonReader to load the map data into a tile
    tile_ = json_reader_.ReadGraphTile(edge_file, node_file);

    if (tile_ != nullptr) {
        tile_cache_->Put(tile_->header()->tile_id.value, tile_);
    }
    return tile_ != nullptr;
}

DirectEdgeInfoPtr GraphReader::GetDirectEdgeInfo(const EdgeId& edge_id) {
    if (!tile_cache_->IsTileExist(edge_id.tileid())) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(edge_id.tileid());
        if (tile_package) {
            auto tile_ptr = std::make_shared<graphTile>(tile_package);
            tile_cache_->Put(tile_ptr->header()->tile_id.value, tile_ptr);
        }
    }

    return tile_cache_->GetDirectEdgeInfo(edge_id);
}

NodeInfoPtr GraphReader::GetNodeInfo(const NodeId& node_id) {
    if (!tile_cache_->IsTileExist(node_id.tileid())) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(node_id.tileid());
        if (tile_package) {
            auto tile_ptr = std::make_shared<graphTile>(tile_package);
            tile_cache_->Put(tile_ptr->header()->tile_id.value, tile_ptr);
        }
    }
    return tile_cache_->GetNodeInfo(node_id);
}

graphTilePtr GraphReader::GetTile(const GraphId& graph_id) {
    if (!tile_cache_->IsTileExist(graph_id.tileid())) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(graph_id.tileid());
        if (tile_package) {
            auto tile_ptr = std::make_shared<graphTile>(tile_package);
            tile_cache_->Put(tile_ptr->header()->tile_id.value, tile_ptr);
        }
    }

    return tile_cache_->Get(graph_id.tileid());
}

// todo: return iter of restritions, not copy
std::vector<RestrictionPtr> GraphReader::GetEnterRestrictions(const EdgeId& edge_id, bool forward) {
    auto tile_ptr = GetTile(edge_id);
    if (tile_ptr == nullptr) {
        return {};
    }
    // 0 means the same as link direction
    uint32_t target_edge_dir = forward ? 0 : 1;
    std::vector<RestrictionPtr> restrictions;
    auto first_iter = std::lower_bound(tile_ptr->enter_restrictions_.begin(), tile_ptr->enter_restrictions_.end(), edge_id,
        [](const auto& restriction, const auto& edge_id) {
        return restriction->in_edge_id < edge_id.id();
    });
    auto last_iter = std::upper_bound(tile_ptr->enter_restrictions_.begin(), tile_ptr->enter_restrictions_.end(), edge_id,
        [](const auto& edge_id, const auto& restriction) {
        return edge_id.id() < restriction->in_edge_id;
    });
    // restrictions.insert(restrictions.end(), first_iter, last_iter);
    for (auto iter = first_iter; iter != last_iter; ++iter) {
        if ((*iter)->in_edge_dir == target_edge_dir) {
            restrictions.push_back(*iter);
        }
    }
    return restrictions;
}

std::vector<RestrictionPtr> GraphReader::GetExitRestrictions(const EdgeId& edge_id, bool forward) {
    auto tile_ptr = GetTile(edge_id);
    if (tile_ptr == nullptr) {
        return {};
    }

    // 0 means the same as link direction
    uint32_t target_edge_dir = forward ? 0 : 1;

    std::vector<RestrictionPtr> restrictions;
    auto first_iter = std::lower_bound(tile_ptr->exit_restrictions_.begin(), tile_ptr->exit_restrictions_.end(), edge_id,
        [](const auto& restriction, const auto& edge_id) {
        return restriction->out_edge_id < edge_id.id();
    });
    auto last_iter = std::upper_bound(tile_ptr->exit_restrictions_.begin(), tile_ptr->exit_restrictions_.end(), edge_id,
        [](const auto& edge_id, const auto& restriction) {
        return edge_id.id() < restriction->out_edge_id;
    });
    // restrictions.insert(restrictions.end(), first_iter, last_iter);

    for (auto iter = first_iter; iter != last_iter; ++iter) {
        if ((*iter)->out_edge_dir == target_edge_dir) {
            restrictions.push_back(*iter);
        }
    }

    // // filter by target_edge_dir
    // std::vector<RestrictionPtr> filtered_restrictions;
    // for (const auto& restriction : restrictions) {
    //     if (restriction->out_edge_dir == target_edge_dir) {
    //         filtered_restrictions.push_back(restriction);
    //     }
    // }
    // restrictions = filtered_restrictions;
    return restrictions;
}

bool IsEnterEdge(const NodeInfoPtr& node_ptr, const DirectEdgeInfoPtr& edge) {
    if (node_ptr == nullptr || edge == nullptr) {
        return false;
    }
    if (edge->startnode_ == node_ptr->id.id()) {
        if (edge->direction == 2 || edge->direction == 3) {
            return true;
        }
    } else if (edge->endnode_ == node_ptr->id.id()) {
        if (edge->direction == 1 || edge->direction == 3) {
            return true;
        }
    }
    return false;
}

bool IsExitEdge(const NodeInfoPtr& node_ptr, const DirectEdgeInfoPtr& edge) {
    if (node_ptr == nullptr || edge == nullptr) {
        return false;
    }
    if (edge->startnode_ == node_ptr->id.id()) {
        if (edge->direction == 1 || edge->direction == 3) {
            return true;
        }
    } else if (edge->endnode_ == node_ptr->id.id()) {
        if (edge->direction == 2 || edge->direction == 3) {
            return true;
        }
    }
    return false;
}

bool GraphReader::GetEnterEdges(const NodeInfoPtr& node_ptr, std::vector<DirectEdgeInfoPtr>& edges) {
    if (node_ptr == nullptr) {
        return false;
    }

    for (const auto& edge_id : node_ptr->edge_ids) {
        auto directededge = GetDirectEdgeInfo({node_ptr->id.tileid(), edge_id});
        if (directededge == nullptr) {
            continue;
        }

        if (IsEnterEdge(node_ptr, directededge)) {
            edges.push_back(directededge);
        }
    }
    return true;
}

bool GraphReader::GetExitEdges(const NodeInfoPtr& node_ptr, std::vector<DirectEdgeInfoPtr>& edges) {
    if (node_ptr == nullptr) {
        return false;
    }

    for (const auto& edge_id : node_ptr->edge_ids) {
        auto directededge = GetDirectEdgeInfo({node_ptr->id.tileid(), edge_id});
        if (directededge == nullptr) {
            continue;
        }

        if (IsExitEdge(node_ptr, directededge)) {
            edges.push_back(directededge);
        }
    }
    return true;
}


void GraphReader::WriteRouteData(const std::string& file_name, const PointLL& pt, double radius) {
    // double lon = 121.31604686379432;
    // double lat = 31.189158148023324;
    // double lon = 121.40006;
    // double lat = 31.1717;
    // double radius = 10000/1e5;
    aurora::parser::GeoMbr mbr(pt.x() - radius/1e5, pt.y() - radius/1e5, pt.x() + radius/1e5, pt.y() + radius/1e5);
    
    // Collect all tile packages for each level
    std::vector<aurora::parser::RouteTilePackagePtr> all_tile_packages;
    int32_t level = 0;
    // Level 0
    std::vector<aurora::parser::RouteTileID> tile_ids;
    data_provider_ptr_->GetRouteTileIDsByMBR(0, mbr, tile_ids);
    for (const auto& tile_id : tile_ids) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(tile_id);
        if (tile_package) {
            WriteRouteTilePackage(file_name, tile_package);
        }
    }

    // Level 1
    tile_ids.clear();
    level = 1;
    all_tile_packages.clear();
    data_provider_ptr_->GetRouteTileIDsByMBR(1, mbr, tile_ids);
    for (const auto& tile_id : tile_ids) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(tile_id);
        if (tile_package) {
            WriteRouteTilePackage(file_name, tile_package);
            all_tile_packages.push_back(tile_package);
        }
    }

    // Level 1
    tile_ids.clear();
    level = 1;
    all_tile_packages.clear();
    data_provider_ptr_->GetRouteTileIDsByMBR(1, mbr, tile_ids);
    for (const auto& tile_id : tile_ids) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(tile_id);
        if (tile_package) {
            WriteRouteTilePackage(file_name, tile_package);
            all_tile_packages.push_back(tile_package);
        }
    }

    // Write all edges and nodes to files
    std::string edges_file1 = file_name + "/level_" + std::to_string(level)+ "_all_edges.geojson";
    std::string nodes_file1 = file_name + "/level_" + std::to_string(level)+ "_all_nodes.geojson";

    std::cout << "Writing all edges to: " << edges_file1 << std::endl;
    GeoJsonWritter::WriteRouteTileEdges(all_tile_packages, edges_file1);

    std::cout << "Writing all nodes to: " << nodes_file1 << std::endl;
    GeoJsonWritter::WriteRouteTileNodes(all_tile_packages, nodes_file1);

    // Level 2
    level = 2;
    tile_ids.clear();
    all_tile_packages.clear();
    data_provider_ptr_->GetRouteTileIDsByMBR(2, mbr, tile_ids);
    for (const auto& tile_id : tile_ids) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(tile_id);
        if (tile_package) {
            WriteRouteTilePackage(file_name, tile_package);
            all_tile_packages.push_back(tile_package);
        }
    }

    // Write all edges and nodes to files
    std::string edges_file = file_name + "/level_" + std::to_string(level)+ "_all_edges.geojson";
    std::string nodes_file = file_name + "/level_" + std::to_string(level)+ "_all_nodes.geojson";
    
    std::cout << "Writing all edges to: " << edges_file << std::endl;
    GeoJsonWritter::WriteRouteTileEdges(all_tile_packages, edges_file);
    
    std::cout << "Writing all nodes to: " << nodes_file << std::endl;
    GeoJsonWritter::WriteRouteTileNodes(all_tile_packages, nodes_file);

    // Also write individual tile files using the existing method
    // for (const auto& tile_package : all_tile_packages) {
    //     WriteRouteTilePackage(file_name, tile_package);
    // }


    // {
    //     double lon = 121.29811;
    //     double lat = 31.14291;
    //     aurora::parser::GeoMbr mbr(lon - 0.001, lat - 0.001, lon + 0.001, lat + 0.001);
    //     std::vector<aurora::parser::RouteTileID> tile_ids;
    //     aurora::parser::DataProvider::Instance().GetRouteTileIdsByMBR(0, mbr, tile_ids);
    //     for (const auto& tile_id : tile_ids) {
    //         auto tile_package = aurora::parser::DataProvider::Instance().GetRouteTileByID(tile_id);
    //         if (tile_package) {
    //             WriteRouteTilePackage("/mnt/d/osm/map_engine/route_data_1.geojson", tile_package);
    //         }
    //     }
    // }
}

void GraphReader::WriteRouteTilePackage(const std::string& file_name, const aurora::parser::RouteTilePackagePtr& tile_package) {
    auto reader = std::make_shared<parser::RouteTileReader>();
    reader->SetTarget(tile_package);
    std::string name = std::to_string(reader->GetTileID().adcode) + "_" +
        std::to_string(reader->GetTileID().level) + "_" + std::to_string(reader->GetTileID().mesh_col) +
        "_" + std::to_string(reader->GetTileID().mesh_row) + "_" + std::to_string(reader->GetTileID().tile_id);
    std::string edge_name = file_name + "/" + name + "_edge.geojson";
    std::string node_name = file_name + "/" + name + "_node.geojson";
    std::cout << "WriteRouteTileEdge: " << edge_name << std::endl;
    GeoJsonWritter::WriteRouteTileEdge(tile_package, edge_name);
    GeoJsonWritter::WriteRouteTileNode(tile_package, node_name);
}

bool GraphReader::LoadTileData(const PointLL& pointll, uint8_t level, double radius) {
    aurora::parser::GeoMbr mbr(pointll.x() - radius/1e5, pointll.y() - radius/1e5, pointll.x() + radius/1e5, pointll.y() + radius/1e5);
    std::vector<aurora::parser::RouteTileID> tile_ids;
    data_provider_ptr_->GetRouteTileIDsByMBR(0, mbr, tile_ids);
    for (const auto& tile_id : tile_ids) {
        auto tile_package = data_provider_ptr_->GetRouteTileByID(tile_id);

        if (tile_package) {
            auto tile_ptr = std::make_shared<graphTile>(tile_package);
            tile_cache_->Put(tile_ptr->header()->tile_id.value, tile_ptr);
        }
    }

    return tile_ids.size() > 0;
}

/**
 * Calculate the projection distance and point from a point to an edge
 * @param pt The point to project
 * @param edge_geos The edge geometry points
 * @param proj_dis The projection distance (output)
 * @param proj_pt The projection point (output)
 * @param proj_index The projection index (output)
 */
void GraphReader::proj2d(PointLL pt, std::vector<PointLL> edge_geos, double& proj_dis, PointLL& proj_pt, size_t& proj_index) {
    // Initialize with a large value
    proj_dis = std::numeric_limits<double>::max();
    
    // Handle edge cases
    if (edge_geos.size() < 2) {
        if (edge_geos.size() == 1) {
            // If only one point, calculate direct distance
            proj_pt = edge_geos[0];
            proj_dis = pt.Distance(edge_geos[0]);
        }
        return;
    }
    
    // Check each segment of the edge
    for (size_t i = 0; i < edge_geos.size() - 1; i++) {
        // Create line segment
        aurora::LineSegment2<PointLL> segment(edge_geos[i], edge_geos[i+1]);
        
        // Calculate closest point and distance
        PointLL closest;
        double dist = segment.Distance(pt, closest);
        
        // Update if this is closer than previous best
        if (dist < proj_dis) {
            proj_dis = dist;
            proj_pt = closest;
            proj_index = i;
        }
    }
}

bool GraphReader::GetLinksByRange(const PointLL& pt, double radius, std::vector<DirectEdgeInfoPtr>& edges) {
    // get interact tileid, and for eachtile , do indextile search
    aurora::parser::GeoMbr mbr(pt.x() - radius/1e5, pt.y() - radius/1e5, pt.x() + radius/1e5, pt.y() + radius/1e5);
    std::vector<aurora::parser::RouteTileID> tile_ids;
    data_provider_ptr_->GetRouteTileIDsByMBR(0, mbr, tile_ids);
    // auto index_tile = index_tile_map_[tile_ids.   tile_ids ->id.id];
    // index_tile->GetLinksByRange(pt, radius, edges);

    for (const auto& tile_id : tile_ids) {
        // check if tile_id exist in tile_cache_
        auto tile_ptr = tile_cache_->Get(tile_id.value);
        if (tile_ptr) {
            // check if tile_id exist in index_tile_map_
            if (index_tile_map_.find(tile_ptr->header()->tile_id.value) == index_tile_map_.end()) {
                // construct index tile
                // IndexTile index_tile(tile);
                auto index_tile = std::make_shared<IndexTile>(tile_ptr);
                index_tile_map_[tile_ptr->header()->tile_id.value] = index_tile;
            }
            auto index_tile = index_tile_map_[tile_ptr->header()->tile_id.value];
            index_tile->Search(pt, radius/1e5, edges);
            if (!edges.empty()) {
                break;
            }
        }
    }

    // filter by proj to edges
    std::vector<DirectEdgeInfoPtr> filtered_edges;
    double min_proj_dis = std::numeric_limits<double>::max();
    DirectEdgeInfoPtr closest_edge = nullptr;
    PointLL closest_proj_pt;
    size_t closest_proj_index;

    for (const auto& edge : edges) {
        double proj_dis;
        PointLL proj_pt;
        size_t proj_index;
        // Calculate projection distance and point
        proj2d(pt, edge->geos, proj_dis, proj_pt, proj_index);
        
        // Keep track of the closest edge
        if (proj_dis < min_proj_dis) {
            min_proj_dis = proj_dis;
            closest_edge = edge;
            closest_proj_pt = proj_pt;
            closest_proj_index = proj_index;
        }
    }

    // print linepoint of closest_proj_pt
    LOG_INFO("closest_proj_pt: {}, {}", closest_proj_pt.x(), closest_proj_pt.y());
    
    // Only keep the closest edge
    if (closest_edge) {
        // calc the offset from the first point of edge to proj point
        double offset = 0.0;
        for (size_t i = 0; i < closest_proj_index; i++) {
            offset += closest_edge->geos[i].Distance(closest_edge->geos[i+1]);
        }
        offset += closest_proj_pt.Distance(closest_edge->geos[closest_proj_index]);
        LOG_INFO("offset: {}", offset);
        filtered_edges.push_back(closest_edge);
    }
    
    edges = filtered_edges;
    return !edges.empty();
}

bool GraphReader::StaticMatching(PathLandmarkPtr landmark, double radius) {
    if (landmark == nullptr || !landmark->valid) {
        return false;
    }

    const auto& pt = landmark->pt;
    double match_radius = radius / 1e5;

    std::vector<DirectEdgeInfoPtr> total_edges;
    for (double search_radius = match_radius; search_radius < 2000.0f; search_radius += match_radius) {
    aurora::parser::GeoMbr mbr(pt.x() - search_radius, pt.y() - search_radius, pt.x() + search_radius, pt.y() + search_radius);
    std::vector<aurora::parser::RouteTileID> tile_ids;
    data_provider_ptr_->GetRouteTileIDsByMBR(0, mbr, tile_ids);
    // auto index_tile = index_tile_map_[tile_ids.   tile_ids ->id.id];
    // index_tile->GetLinksByRange(pt, radius, edges);
    std::vector<DirectEdgeInfoPtr> edges;
    for (const auto& tile_id : tile_ids) {
        // check if tile_id exist in tile_cache_
        if (!tile_cache_->IsTileExist(tile_id.value)) {
            auto tile_package = data_provider_ptr_->GetRouteTileByID(tile_id);
            if (tile_package) {
                auto tile_ptr = std::make_shared<graphTile>(tile_package);
                tile_cache_->Put(tile_ptr->header()->tile_id.value, tile_ptr);
            }
        }

        auto tile_ptr = tile_cache_->Get(tile_id.value);
        if (tile_ptr) {
            // check if tile_id exist in index_tile_map_
            if (index_tile_map_.find(tile_ptr->header()->tile_id.value) ==
                index_tile_map_.end()) {
                // construct index tile
                // IndexTile index_tile(tile);
                auto index_tile = std::make_shared<IndexTile>(tile_ptr);
                index_tile_map_[tile_ptr->header()->tile_id.value] = index_tile;
            }
            auto index_tile = index_tile_map_[tile_ptr->header()->tile_id.value];
            index_tile->Search(pt, search_radius, edges);
        }
    }
    if (!edges.empty()) {
        total_edges = edges;
        break;
    }
    }

    // filter by proj to edges
    std::vector<DirectEdgeInfoPtr> filtered_edges;
    double min_proj_dis = std::numeric_limits<double>::max();
    DirectEdgeInfoPtr closest_edge = nullptr;
    PointLL closest_proj_pt;
    size_t closest_proj_index;

    for (const auto& edge : total_edges) {
        // if (edge->is_area_link) {
        //     continue;
        // }
        double proj_dis;
        PointLL proj_pt;
        size_t proj_index;
        // Calculate projection distance and point
        proj2d(pt, edge->geos, proj_dis, proj_pt, proj_index);
        
        // Keep track of the closest edge
        if (proj_dis < min_proj_dis) {
            min_proj_dis = proj_dis;
            closest_edge = edge;
            closest_proj_pt = proj_pt;
            closest_proj_index = proj_index;
        }
    }

    if (closest_edge) {
        Candidate candidate;
        // calc the offset from the first point of edge to proj point
        double offset = 0.0;
        for (size_t i = 0; i < closest_proj_index; i++) {
            offset += closest_edge->geos[i].Distance(closest_edge->geos[i+1]);
        }
        offset += closest_proj_pt.Distance(closest_edge->geos[closest_proj_index]);
        filtered_edges.push_back(closest_edge);
        candidate.offset = std::fmin(offset, closest_edge->length_);
        candidate.proj_index = closest_proj_index;
        candidate.proj_pt = closest_proj_pt;
        candidate.link = std::make_shared<EdgeInfo>();
        candidate.link->tile_id = closest_edge->id.tileid();
        candidate.link->id = closest_edge->local_idx;
        candidate.link->geos = closest_edge->geos;
        candidate.link->length = closest_edge->length_;
        candidate.link->direction = closest_edge->direction;
        landmark->candidates.push_back(candidate);
        LOG_INFO("Matched pt: ({} {}), closest_proj_pt: {}, {}, graph_id: {}, offset: {}, min_proj_dis: {}", pt.x(), pt.y(),
            closest_proj_pt.x(), closest_proj_pt.y(), closest_edge->id.ToString(), offset,
            min_proj_dis);
        return true;
    }
    return false;
}

} // namespace path
} // namespace aurora
