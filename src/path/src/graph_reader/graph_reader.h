// Copyright (c) 2025 BYD Corporation. All rights reserved.
// Author: <EMAIL>
//
// Created: 2025-05-12

#ifndef AURORA_PATH_SRC_GRAPH_READER_GRAPH_READER_H_
#define AURORA_PATH_SRC_GRAPH_READER_GRAPH_READER_H_

#include <string>
#include <memory>
#include <unordered_map>
#include "../base/data_interface.h"
#include "../debug/geojson_reader.h"
#include "tilecache.h"
#include "data_provider.h"
#include "base/include/logger.h"
#include "../debug/geojson_writter.h"
#include "base/index_tile.h"
#include "path_def.h"
#include "../config/path_config.h"

namespace aurora {
namespace path {

using EdgeId = GraphId;
using NodeId = GraphId;
class GraphReader {
public:
    GraphReader();

    GraphReader(const std::string& data_dir);
    ~GraphReader();

    bool InitDataProvider();

    // Load map data from GeoJSON files
    bool LoadGeoJsonMap(const std::string& edge_file, const std::string& node_file);

    bool LoadTileData(const PointLL& pointll, uint8_t level, double radius);

    bool GetLinksByRange(const PointLL& pt, double radius, std::vector<DirectEdgeInfoPtr>& edges);

    /**
     * Calculate the projection distance and point from a point to an edge
     * @param pt The point to project
     * @param edge_geos The edge geometry points
     * @param proj_dis The projection distance (output)
     * @param proj_pt The projection point (output)
     * @param proj_index The projection index (output)
     */
    void proj2d(PointLL pt, std::vector<PointLL> edge_geos, double& proj_dis, PointLL& proj_pt, size_t& proj_index);

    // Get edge information by edge ID
    DirectEdgeInfoPtr GetDirectEdgeInfo(const EdgeId& edge_id);

    // Get node information by node ID
    NodeInfoPtr GetNodeInfo(const NodeId& node_id);

    graphTilePtr GetTile(const GraphId& graph_id);

    std::vector<RestrictionPtr> GetEnterRestrictions(const EdgeId& edge_id, bool forward);

    std::vector<RestrictionPtr> GetExitRestrictions(const EdgeId& edge_id, bool forward);

    bool GetEnterEdges(const NodeInfoPtr& node_ptr, std::vector<DirectEdgeInfoPtr>& edges);

    bool GetExitEdges(const NodeInfoPtr& node_ptr, std::vector<DirectEdgeInfoPtr>& edges);


    // Write route data to file
    void WriteRouteData(const std::string& file_name, const PointLL& pt, double radius);

    // Write route tile package to file
    void WriteRouteTilePackage(const std::string& file_name, const aurora::parser::RouteTilePackagePtr& tile_package);


    bool StaticMatching(PathLandmarkPtr landmark, double radius = 10.0f);

private:
    // Store the graph tile
    graphTilePtr tile_;

    // GeoJSON reader for loading map data
    GeoJsonReader json_reader_;

    TileCachePtr tile_cache_;

    std::unordered_map<uint64_t, IndexTilePtr> index_tile_map_;

    std::shared_ptr<aurora::parser::DataProvider> data_provider_ptr_;

    std::string data_dir_ = "../route"; // Default value
};
using GraphReaderPtr = std::shared_ptr<GraphReader>;

} // namespace path
} // namespace aurora

#endif  // AURORA_PATH_SRC_GRAPH_READER_GRAPH_READER_H_
