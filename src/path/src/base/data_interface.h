// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-04-30
//

#ifndef AURORA_PATH_INCLUDE_DATA_INTERFACE_H_
#define AURORA_PATH_INCLUDE_DATA_INTERFACE_H_

#include <algorithm>
#include <vector>
#include <string>
#include <memory>
#include <cassert>
#include <iostream>
#include <fstream>
#include <iomanip>
#include "logger.h"


#include "base/include/module.h"
#include "graphid.h"
#include "base/include/pointll.h"
#include "data_provider/include/data_provider.h"
#include "data_provider/include/route_data/route_tile_reader.h"
// #include "data_provider/route_data/route_tile_package.h"

namespace aurora {
namespace path {


// using GraphId = uint64_t;
// struct PointLL {
//     double lat;
//     double lng;
// };  // struct PointLL


// struct GeoPoint {
//     GeoPoint() : lat(0), lng(0) {}
//     GeoPoint(double lat, double lng) : lat(lat), lng(lng) {}
//     double lat;
//     double lng;
// };  // struct GeoPoint

// struct Link {
//     std::string id;
//     PointLL start;
//     PointLL end;
// };  // struct Link
// using LinkPtr = std::shared_ptr<Link>;

struct Restriction {
    // MapId from;  // 起始边ID
    // MapId  to;    // 目标边ID
    // std::vector<MapId > vias; // 必经边序列 // Inner link
    // bool has_dt; // 是否有日期时间信息
    // // TimeRestriction time; //时间相关字段

    uint32_t in_tile_id : 8;   ///< Tile identifier for the incoming edge (8 bits)
    uint32_t in_edge_dir : 1;  ///< Direction flag of the incoming edge (forward/reverse)
    uint32_t in_edge_id : 23;  ///< Unique identifier of the incoming edge within its tile (23 bits)
  
    uint32_t out_tile_id : 8;   ///< Tile identifier for the outgoing edge (8 bits)
    uint32_t out_edge_dir : 1;  ///< Direction flag of the outgoing edge (forward/reverse)
    uint32_t out_edge_id : 23;  ///< Unique identifier of the outgoing edge within its tile (23 bits)
    uint8_t access_ctrl_type : 3;      ///< defined in enum AccessCtrlType
    uint8_t access_ctrl_relation : 1;  ///< defined in enum AccessCtrlRelation

    Restriction(const parser::LimitPassBase* base) {
        in_tile_id = base->in_tile_id;
        in_edge_dir = base->in_edge_dir;
        in_edge_id = base->in_edge_id;
        out_tile_id = base->out_tile_id;
        out_edge_dir = base->out_edge_dir;
        out_edge_id = base->out_edge_id;
        access_ctrl_type = base->access_ctrl_type;
        access_ctrl_relation = base->access_ctrl_relation;
    }

    std::string ToString() {
        return std::to_string(in_tile_id) + "_" + std::to_string(in_edge_id) + "_" + std::to_string(in_edge_dir) + "_" +
               std::to_string(out_tile_id) + "_" + std::to_string(out_edge_id) + "_" + std::to_string(out_edge_dir) + "_" +
               std::to_string(static_cast<int>(access_ctrl_type)) + "_" + std::to_string(static_cast<int>(access_ctrl_relation)) + ";";
    }
};
using RestrictionPtr = std::shared_ptr<Restriction>;

struct DirectEdgeInfo {
    bool is_forward() {
        return direction & 0x01;
    }

    bool is_reverse() {
        return direction & 0x02;
    }

    GraphId id;
    // LinkPtr link;


    // uint32_t tile_id : 16;
    uint32_t level_id;
    uint32_t local_idx;
    uint32_t opp_index;
    uint32_t opp_local_index;

    uint16_t length_;        // Length in meters
    // uint32_t speed_;                  // Speed (kph)
    
    uint32_t startnode_;      // End node of the directed edge
    uint32_t endnode_;      // End node of the directed edge

    uint8_t direction;
    uint8_t function_class;

    uint8_t speed_grade;
    uint8_t is_area_link;
    uint8_t road_class;

    uint32_t is_inner_edge;


    // uint8_t function_class : 3;  ///< Road function class
    uint8_t edge_type : 2;       ///< Edge type (0-3)
    // uint8_t direction : 2;       ///< Direction type (0-3)
    uint8_t need_toll : 1;       ///< Flag indicating if the edge requires toll payment (0/1)
  
    // uint32_t start_node_id : 24;        ///< Start node ID
    uint32_t positive_speed_limit : 5;  ///< Positive direction speed limit
    uint32_t is_overhead : 1;           ///< Flag indicating if edge is overhead structure (0/1)
    // uint32_t is_inner_edge : 1;         ///< Flag indicating if edge is inside the intersection  (0/1)
    uint32_t is_separate : 1;  ///< Flag indicating if edge is separated from other lanes (0/1)
  
    // uint32_t end_node_id : 24;         ///< End node ID
    uint32_t negtive_speed_limit : 5;  ///< Reverse direction speed limit
    // uint8_t is_area_edge : 1;          ///< Flag indicating if edge is part of area boundary (0/1)
    uint8_t is_city_edge : 1;          ///< Flag indicating if edge is within city limits (0/1)
    uint8_t is_ramp : 1;               ///< Flag indicating if edge is a ramp (0/1)
  
    uint8_t edge_form : 5;    ///< Edge geometry type
    // uint8_t speed_grade : 3;  ///< Speed grade
    // uint16_t length;          ///< Edge length in meters
  
    uint8_t forward_lane_count : 2;   ///< Forward direction lanes
    uint8_t backward_lane_count : 2;  ///< Backward direction lanes
    uint8_t lane_count : 4;           ///< Total lanes
  
    // uint8_t road_class : 4;        ///< Road hierarchy class
    uint8_t is_left : 1;           ///< Flag indicating left-hand traffic (0/1)
    uint8_t has_turn_rule : 1;     ///< Flag indicating presence of turn restrictions (0/1)
    uint8_t is_time_limit : 1;     ///< Flag indicating time-based restrictions (0/1)
    uint8_t is_all_day_limit : 1;  ///< Flag indicating 24/7 restrictions (0/1)
  
    uint8_t is_building : 1;  ///< Flag indicating edge is adjacent to buildings (0/1)
    uint8_t is_paved : 1;     ///< Flag indicating surface is paved (0/1)
    uint8_t is_gate : 1;      ///< Flag indicating presence of controlled gate (0/1)
    uint8_t no_crossing : 1;  ///< Flag indicating no pedestrian crossing (0/1)
    uint8_t is_private : 1;   ///< Flag indicating private access only (0/1)



    std::string name;
    std::vector<PointLL> geos;
};  // struct DirectEdgeInfo

struct NodeInfo {
    GraphId id;
    PointLL point;

    uint32_t tile_id : 16;
    uint32_t level;
    uint32_t local_idx;
    uint32_t edge_idx : 12;
    uint32_t edge_cnt : 7;
    uint32_t access : 12;
    uint32_t type : 4;
    uint32_t transition_index : 21;
    uint32_t local_edge_count : 3;
    uint32_t has_traffic_light : 1;
    uint16_t is_transidown : 1;
    uint16_t is_transiup : 1;
    std::vector<uint32_t> edge_ids;
    std::vector<uint32_t> angles;
    std::vector<GraphId> boundary_node_ids;
    bool is_tranup;
    GraphId tranup_id;
    bool is_trandown;
    GraphId transdown_id;


};  // struct NodeInfo

// struct TileId {
//     uint32_t id;
//     uint8_t level;
// };  // struct TileId

struct GraphHeader {
    uint32_t node_count;
    uint32_t edge_count;
    parser::RouteTileID tile_id;

    // todo: direct edge count , use dir to split edge

    uint32_t directededgecount() {
        return edge_count;
    }
};  // struct GraphHeader
using GraphHeaderPtr = std::shared_ptr<GraphHeader>;

using DirectEdgeInfoPtr = std::shared_ptr<DirectEdgeInfo>;
using NodeInfoPtr = std::shared_ptr<NodeInfo>;
struct graphTile {

    graphTile() {
        tile_header = std::make_shared<GraphHeader>();
        reader = std::make_shared<parser::RouteTileReader>();
    }

    graphTile(parser::RouteTilePackagePtr tile_package) {
        tile_header = std::make_shared<GraphHeader>();
        reader = std::make_shared<parser::RouteTileReader>();

        // todo:
        // id.id = tile_package->GetTileID().tile_id;
        // id.level = tile_package->GetTileID().level;
        reader->SetTarget(tile_package);

        tile_header->tile_id = reader->GetTileID();

        std::vector<parser::RouteNode>& nodes = reader->GetNodes();
        int node_count = nodes.size();
        tile_header->node_count = node_count;
        nodes_.reserve(node_count);
        for (int i = 0; i < node_count; ++i) {
            auto& current_node = nodes[i];
            auto new_node = std::make_shared<NodeInfo>();
            // todo: add featureID
            new_node->id = GraphId(tile_header->tile_id.value, current_node.GetID());
            new_node->local_idx = current_node.GetID();
            new_node->level = tile_header->tile_id.level;

            auto point = current_node.GetPosition();
            PointLL node_pt = PointLL(static_cast<double>(point.x()),
                                      static_cast<double>(point.y()));
            new_node->point = std::move(node_pt);

            auto* base_info = current_node.GetBaseInfo();
            new_node->has_traffic_light = base_info->has_traffic_light;

            auto connected_links = current_node.GetConnectedEdge();
            new_node->edge_cnt = connected_links.size();
            for (size_t idx = 0; idx < connected_links.size(); ++idx) {
                auto& connected_link = connected_links.at(idx);
                new_node->edge_ids.push_back(connected_link->edge_id);
                new_node->angles.push_back(connected_link->angle);
            }

            auto boundary_nodes = current_node.GetBoundaryNode();
            for (size_t idx = 0; idx < boundary_nodes.size(); ++idx) {
                auto& boundary_node = boundary_nodes.at(idx);
                auto adj_tile_id = tile_header->tile_id;
                adj_tile_id.tile_id = boundary_node->adj_tile_id;
                adj_tile_id.mesh_col = boundary_node->adj_mesh_col;
                adj_tile_id.mesh_row = boundary_node->adj_mesh_row;
                GraphId adj_id(adj_tile_id.value, boundary_node->adj_node_id);
                // new_node->boundary_node_ids.push_back(boundary_node->adj_node_id);

                new_node->boundary_node_ids.push_back(adj_id);
            }

            if (base_info->is_admin_boundary) {
                auto adboundary_node = current_node.GetAdBoundaryNode();
                auto adj_tile_id = tile_header->tile_id;
                adj_tile_id.tile_id = adboundary_node->adj_tile_id;
                adj_tile_id.mesh_col = adboundary_node->adj_mesh_col;
                adj_tile_id.mesh_row = adboundary_node->adj_mesh_row;
                adj_tile_id.adcode = adboundary_node->adcode;

                GraphId adj_id(adj_tile_id.value, adboundary_node->adj_node_id);
                new_node->boundary_node_ids.push_back(adj_id);
            }

            auto transup_node = current_node.GetTransUpNodeInfo();
            if (transup_node) {
                new_node->is_tranup = true;
                parser::RouteTileID up_tile_Id = tile_header->tile_id;
                up_tile_Id.level +=1;
                up_tile_Id.tile_id = transup_node->opp_tile_id;
                new_node->tranup_id = GraphId(up_tile_Id.value, transup_node->opp_node_id);
            } else {
                new_node->is_tranup = false;
            }
            auto transdown_node = current_node.GetTransDownNodeInfo();
            if (transdown_node) {
                new_node->is_trandown = true;
                parser::RouteTileID down_tile_Id = tile_header->tile_id;
                down_tile_Id.level -=1;
                down_tile_Id.tile_id = transdown_node->opp_tile_id;
                new_node->transdown_id = GraphId(down_tile_Id.value, transdown_node->opp_node_id);
            } else {
                new_node->is_trandown = false;
            }

            nodes_.push_back(new_node);
        }

        auto& topo_links =  reader->GetTopolEdges();
        auto& topo_augment_links = reader->GetAugmentEdges();
        tile_header->edge_count = topo_links.size();
        int link_count = topo_links.size();
        edges_.reserve(link_count);
        for (int i = 0; i < link_count; ++i) {
            auto& aug_link = topo_augment_links[i];
            auto& current_link = topo_links.at(i);
            auto* baseinfo = current_link.GetBaseInfo();
            auto new_edge = std::make_shared<DirectEdgeInfo>();
            new_edge->id = GraphId(tile_header->tile_id.value, current_link.GetID());
            new_edge->level_id = tile_header->tile_id.level;
            new_edge->local_idx = current_link.GetID();
            assert(current_link.GetID() == aug_link.GetID());

            new_edge->length_ = baseinfo->length;
            new_edge->startnode_ = baseinfo->start_node_id;
            new_edge->endnode_ = baseinfo->end_node_id;
            new_edge->is_inner_edge =  baseinfo->is_inner_edge;

            new_edge->direction = static_cast<int8_t>(baseinfo->direction);
            new_edge->function_class = baseinfo->function_class;
            new_edge->speed_grade = baseinfo->speed_grade;
            new_edge->is_area_link = baseinfo->is_area_edge;
            new_edge->road_class = baseinfo->road_class;


            new_edge->edge_type = baseinfo->edge_type;
            new_edge->need_toll = baseinfo->need_toll;
            new_edge->positive_speed_limit = baseinfo->positive_speed_limit;
            new_edge->is_overhead = baseinfo->is_overhead;
            new_edge->is_separate = baseinfo->is_separate;
            new_edge->negtive_speed_limit = baseinfo->negtive_speed_limit;
            new_edge->is_city_edge = baseinfo->is_city_edge;
            new_edge->is_ramp = baseinfo->is_ramp;
            new_edge->edge_form = baseinfo->edge_form;

            new_edge->forward_lane_count = baseinfo->forward_lane_count;
            new_edge->backward_lane_count = baseinfo->backward_lane_count;
            new_edge->lane_count = baseinfo->lane_count;

            new_edge->is_left = baseinfo->is_left;
            new_edge->has_turn_rule = baseinfo->has_turn_rule;
            new_edge->is_time_limit = baseinfo->is_time_limit;
            new_edge->is_all_day_limit = baseinfo->is_all_day_limit;

            new_edge->is_building = baseinfo->is_building;
            new_edge->is_paved = baseinfo->is_paved;

            new_edge->is_gate = baseinfo->is_gate;
            new_edge->no_crossing = baseinfo->no_crossing;
            new_edge->is_private = baseinfo->is_private;

            auto points = aug_link.GetGeoPoints();
            for (const auto& point : points) {
                new_edge->geos.push_back(PointLL(static_cast<double>(point.x()),
                                                static_cast<double>(point.y())));
            }
            // if (new_edge->local_idx ==0) {
            //     std::stringstream ss;
            //     ss << "LineString (";
            //     for (size_t idx = 0; idx < new_edge->geos.size(); ++idx)
            //     {
            //         if (idx > 0)
            //         {
            //             ss << ", ";
            //         }
            //         ss << std::fixed << std::setprecision(10) << new_edge->geos[idx].x() << " " << new_edge->geos[idx].y();
            //     }
            //     ss << ")";
            //     std::string line_string = ss.str();
            //     std::cout << std::fixed << std::setprecision(10) << line_string << std::endl;
            // }

            edges_.push_back(new_edge);
        }

        auto&limit_pass = reader->GetLimitPass();
        LOG_INFO("limit pass size: {}", limit_pass.size());

        for (size_t idx = 0; idx < limit_pass.size(); ++idx) {
            auto& limit = limit_pass.at(idx);
            const auto* base = limit.GetBaseInfo();
            auto restriction = std::make_shared<Restriction>(base);
            enter_restrictions_.push_back(restriction);
        }

        exit_restrictions_ = enter_restrictions_;
        std::sort(exit_restrictions_.begin(), exit_restrictions_.end(), [](const auto& a, const auto& b) {
            return a->out_edge_id < b->out_edge_id;
        });
        // write to file
        // std::ofstream file("/mnt/d/osm/map_engine/debug/restrictions.txt", std::ios::app);
        // if (!file.is_open()) {
        //     return;
        // }
        // for (const auto& restriction : enter_restrictions_) {
        //     file << restriction->in_tile_id << " " << restriction->in_edge_dir << " " << restriction->in_edge_id << " "
        //          << restriction->out_tile_id << " " << restriction->out_edge_dir << " " << restriction->out_edge_id << " "
        //          << static_cast<int>(restriction->access_ctrl_type) << " "
        //          << static_cast<int>(restriction->access_ctrl_relation)
        //          << std::endl;
        // }
        // file.close();
    }

    // TileId id;
    // std::vector<GraphId> edges;
    GraphHeaderPtr tile_header;
    std::shared_ptr<parser::RouteTileReader> reader;
    GraphHeaderPtr header() {
        return tile_header;
    }
    std::vector<DirectEdgeInfoPtr> edges_;
    std::vector<NodeInfoPtr> nodes_;
    std::vector<RestrictionPtr> enter_restrictions_;
    std::vector<RestrictionPtr> exit_restrictions_;
    std::vector<DirectEdgeInfoPtr>& edges() {
        return edges_;
    }

    std::vector<NodeInfoPtr>& nodes() {
        return nodes_;
    }
};  // struct graphTile

using graphTilePtr = std::shared_ptr<graphTile>;

class DataInterface : public IInterface {
    public:
      const DirectEdgeInfoPtr edgeinfo(const GraphId& edgeid);
      const NodeInfoPtr nodeinfo(const GraphId& nodeid);
      const DirectEdgeInfoPtr GetOpposingEdgeId(const GraphId& edgeid);
      const std::vector<DirectEdgeInfoPtr> getEdgesByRange(PointLL lonlat, double dis);
      
         // get all tiles id
         std::vector<parser::RouteTileID> GetAllTiles();
        // 根据GraphId获取图块
        graphTilePtr GetGraphTile(const GraphId& tileId);
        // 根据经纬度和层级获取图块
        graphTilePtr GetGraphTile(const PointLL& pointll, const uint8_t level);
};



}  // namespace path
}  // namespace aurora

#endif  // AURORA_PATH_INCLUDE_DATA_INTERFACE_H_