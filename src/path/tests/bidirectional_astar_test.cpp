// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-05-30
//

#include <gtest/gtest.h>
#include <memory>
#include <string>
#include <chrono>
#include <thread>
#include <filesystem>
#include <mutex>
#include <condition_variable>

#include "path/include/path_module.h"
#include "path/include/path_def.h"
#include "pointll.h"
#include "logger.h"

namespace aurora {
namespace path {
namespace test {

// Test fixture for PathModule tests
class BiDirectionalAstarTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create unique log file for each test to avoid conflicts
        std::string log_file = "logs/path_module_test_" + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count()) + ".log";

        // // Initialize logger for tests
        // if (!aurora::logger::get().init(log_file)) {
        //     std::cerr << "Failed to initialize logger" << std::endl;
        // }
        // aurora::logger::get().set_level(spdlog::level::info);

        // Create test config file path
        config_file_ = "/home/<USER>/dlc/map_engine/src/path/config/path.yaml";
        data_dir_ = "/home/<USER>/dlc/map_engine/distribution/data/route";

        // Verify config file exists
        if (!std::filesystem::exists(config_file_)) {
            GTEST_SKIP() << "Config file not found: " << config_file_;
        }

        // Verify data directory exists
        if (!std::filesystem::exists(data_dir_)) {
            GTEST_SKIP() << "Data directory not found: " << data_dir_;
        }

        // Create PathModule instance
        path_module_ = std::make_shared<PathModule>();

        // Create test listener
        test_listener_ = std::make_shared<TestPathListener>();
    }

    void TearDown() override {
        if (path_module_) {
            auto path_interface = std::dynamic_pointer_cast<PathInterface>(path_module_->GetInterface());
            if (path_interface && test_listener_) {
                path_interface->RemovePathListener(test_listener_);
            }
            // Only stop and uninit if the module was actually started
            if (path_module_->IsInit() == ModuleInitStatus::kModuleInitDone) {
                path_module_->Stop();
                path_module_->UnInit();
            }
        }
        // aurora::logger::get().shutdown();
    }

    // Test listener implementation
    class TestPathListener : public PathListener {
    public:
        void OnPathResult(const PathQueryPtr& query, const PathResultPtr& result) override {
            std::lock_guard<std::mutex> lock(mutex_);
            last_query_ = query;
            last_result_ = result;
            result_received_ = true;
            cv_.notify_one();
        }

        bool WaitForResult(int timeout_ms = 5000) {
            std::unique_lock<std::mutex> lock(mutex_);
            return cv_.wait_for(lock, std::chrono::milliseconds(timeout_ms),
                               [this] { return result_received_; });
        }

        PathResultPtr GetLastResult() {
            std::lock_guard<std::mutex> lock(mutex_);
            return last_result_;
        }

        void Reset() {
            std::lock_guard<std::mutex> lock(mutex_);
            result_received_ = false;
            last_query_.reset();
            last_result_.reset();
        }

    private:
        std::mutex mutex_;
        std::condition_variable cv_;
        bool result_received_ = false;
        PathQueryPtr last_query_;
        PathResultPtr last_result_;
    };

    // Helper function to create a basic path query
    PathQueryPtr CreatePathQuery(const PointLL& start_pt, const PointLL& end_pt) {
        auto query = std::make_shared<PathQuery>();

        // Set basic query parameters
        query->strategy = PathStrategy::kTimeFirst;
        query->trigger = PathTrigger::kInitialRouting;
        query->mode = PathMode::kOffline;
        query->date_option.type = DateTimeType::kCurrent;

        // Create start point
        auto start = std::make_shared<PathLandmark>();
        start->valid = true;
        start->waypoint_type = WayPointType::kStartPoint;
        start->landmark_type = LandmarkType::kClick;
        start->pt = start_pt;
        start->name = "Start Point";

        // Create end point
        auto end = std::make_shared<PathLandmark>();
        end->valid = true;
        end->waypoint_type = WayPointType::kEndPoint;
        end->landmark_type = LandmarkType::kClick;
        end->pt = end_pt;
        end->name = "End Point";

        query->path_points.push_back(start);
        query->path_points.push_back(end);

        return query;
    }

    // Helper function to initialize PathModule
    bool InitializePathModule() {
        // Prepare module with config
        if (path_module_->Prepare(config_file_) != 0) {
            return false;
        }

        // Set parameters
        if (path_module_->SetParams({{"data_dir", data_dir_}}) != 0) {
            return false;
        }

        // Create module finder (returns nullptr for this test)
        auto module_finder = [](aurora::ModuleId id) {
            return nullptr;
        };

        // Initialize module
        if (path_module_->Init(module_finder) != 0) {
            return false;
        }

        // Start module
        if (path_module_->Start() != 0) {
            return false;
        }

        return true;
    }

protected:
    std::shared_ptr<PathModule> path_module_;
    std::shared_ptr<TestPathListener> test_listener_;
    std::string config_file_;
    std::string data_dir_;
};


// Test short distance routing (within same tile) 禁转不生效。
TEST_F(BiDirectionalAstarTest, ShortDistanceRouting) {
    ASSERT_TRUE(InitializePathModule());

    auto path_interface = std::dynamic_pointer_cast<PathInterface>(path_module_->GetInterface());
    ASSERT_TRUE(path_interface != nullptr);

    // Add listener
    EXPECT_TRUE(path_interface->AddPathListener(test_listener_));

    // Create short distance query (within Shanghai, same tile)
    PointLL start(121.3686, 31.184365);   // 动物园
    PointLL end(121.370121, 31.184599);   // 附近点
    auto query = CreatePathQuery(start, end);

    // Request path
    test_listener_->Reset();
    std::string uuid = path_interface->RequestPath(query);
    EXPECT_FALSE(uuid.empty());

    // Wait for result
    ASSERT_TRUE(test_listener_->WaitForResult(10000));

    auto result = test_listener_->GetLastResult();
    ASSERT_TRUE(result != nullptr);
    EXPECT_EQ(result->uuid, uuid);
    EXPECT_FALSE(result->status.empty());
}

} // namespace test
} // namespace path
} // namespace aurora

