cmake_minimum_required(VERSION 3.10)

project(aurora_path)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

include_directories(./include)
include_directories(./src)
include_directories(./src/base)
include_directories(../base/include)
include_directories(${RAPIDJSON_SOURCE_DIR}/include)
include_directories(../data_provider/include)
include_directories(../data_provider/include/route_data)
include_directories(../data_provider/)

# Source files
set(PATH_SRCS
    src/path_module.cpp
    src/path_interface_impl.cpp
    src/route_algorithm/bidirectional_astar.cpp
    src/route_algorithm/dijkstra.cpp
    src/route_algorithm/path_ranking.cpp
    src/route_algorithm/cost/autocost.cpp
    src/route_algorithm/cost/dynamiccost.cpp
    src/debug/geojson_writter.cpp
    src/debug/geojson_reader.cpp
    src/base/index_tile.cpp
    src/graph_reader/graph_reader.cpp
    src/graph_reader/tilecache.cpp
    src/config/path_config.cpp
)

# Header files
set(PATH_HEADERS
    include/path_module.h
    src/path_interface_impl.h
    src/route_algorithm/cost/autocost.h
    src/debug/geojson_writter.h
    src/debug/geojson_reader.h
    src/graph_reader/graph_reader.h
    src/graph_reader/tilecache.h
    src/base/index_tile.h
    src/route_algorithm/dijkstra.h
    src/config/path_config.h
    src/route_algorithm/path_ranking.h
)

# Create library
add_library(${PROJECT_NAME} SHARED ${PATH_SRCS} ${PATH_HEADERS})

# Include directories
target_include_directories(${PROJECT_NAME}
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
        ${RAPIDJSON_SOURCE_DIR}/include
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# Link dependencies
target_link_libraries(${PROJECT_NAME}
    PUBLIC
    data_provider
    aurora_base
    yaml-cpp
)

target_include_directories(${PROJECT_NAME} PRIVATE ${CMAKE_BINARY_DIR}/_deps/yaml-cpp-src/include)


# Create yaml_test executable
# add_executable(yaml_test tools/yaml_test.cpp)
# target_link_libraries(yaml_test yaml-cpp)
# target_include_directories(yaml_test PRIVATE ${CMAKE_BINARY_DIR}/_deps/yaml-cpp-src/include)
# set_target_properties(yaml_test PROPERTIES
#     RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/_deps/yaml-cpp-build/bin
# )

# Enable testing
enable_testing()

# Add tests subdirectory
# set(CMAKE_BUILD_TYPE "Debug")
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0")
add_subdirectory(tests)
add_subdirectory(tools)
add_subdirectory(tools/spdlog_demo)
add_subdirectory(tests/thread_pool_test)

